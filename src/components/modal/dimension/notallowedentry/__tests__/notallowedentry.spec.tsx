import { mount, shallowMount } from '@vue/test-utils';

import QPlainTable from '@/components/global/q-plain-table';
import FileLogo from '@/components/file-logo';
import { numberToHumanWithUnit } from '@/utils/number-formatter';
import NotAllowedEntry from '..';

// Mock utility functions
vi.mock('@/utils/number-formatter', () => ({
  numberToHumanWithUnit: vi.fn((value: number) => {
    if (!value) return null;
    return `${value.toLocaleString()} kg`;
  }),
}));

// Mock constants
vi.mock('@/shared/constants/file-type', () => ({
  FILE_TYPES: ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
}));

// Mock global window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: vi.fn(),
});

describe('NotAllowedEntry 组件测试', () => {
  // 创建基础测试数据
  const createViewData = (overrides = {}) => ({
    ProductName: '测试产品',
    TradeMark: '测试品牌',
    Weight: 1000,
    Origin: '中国',
    ImporterName: '测试进口商',
    Entryport: '上海港',
    Disposemeasure: '退运',
    RefusedReason: '质量不合格',
    SubmissionMonth: '2023-06',
    Attachment: 'https://example.com/document.pdf',
    AttachType: 'pdf',
    HsCode: '1234567890',
    Quarantinecode: 'CIQ123456',
    Eoriimporter: 'EORI123456789',
    ...overrides,
  });

  // 创建参数数据
  const createParameter = (type?: string) => ({
    type,
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('基础渲染测试', () => {
    it('TC01 - 应正确渲染组件基本结构', () => {
      const viewData = createViewData();
      const parameter = createParameter();
      const wrapper = shallowMount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证组件存在
      expect(wrapper.exists()).toBe(true);

      // 验证QPlainTable组件存在
      const plainTable = wrapper.findComponent(QPlainTable);
      expect(plainTable.exists()).toBe(true);
    });

    it('TC02 - 当viewData为空时应返回null', () => {
      const wrapper = shallowMount(NotAllowedEntry, {
        propsData: { viewData: null, parameter: createParameter() },
      });

      // 验证组件不渲染任何内容
      expect(wrapper.html()).toBe('');
    });

    it('TC03 - 当viewData为undefined时应返回null', () => {
      const wrapper = shallowMount(NotAllowedEntry, {
        propsData: { viewData: undefined, parameter: createParameter() },
      });

      // 验证组件不渲染任何内容
      expect(wrapper).toMatchInlineSnapshot(`
        <div class="container">
          <table class="table">
            <tr>
              <th width="23%" class="tb">HS编码</th>
              <td width="27%">-</td>
              <th width="23%" class="tb">检验检疫编号</th>
              <td width="27%">-</td>
            </tr>
            <tr>
              <th class="tb">产品名称</th>
              <td>-</td>
              <th class="tb">产地</th>
              <td>-</td>
            </tr>
            <tr>
              <th class="tb">生产企业信息</th>
              <td colspan="3">-</td>
            </tr>
            <tr>
              <th class="tb">数量/重量</th>
              <td>-</td>
              <th class="tb">进境口岸</th>
              <td>-</td>
            </tr>
            <tr>
              <th class="tb">进口商备案号</th>
              <td colspan="3">-</td>
            </tr>
            <tr>
              <th class="tb">未准入境的事实</th>
              <td colspan="3">-</td>
            </tr>
            <tr>
              <th class="tb">报送时间</th>
              <td>-</td>
              <th class="tb">附件</th>
              <td><span>-</span></td>
            </tr>
          </table>
        </div>
      `);
    });
  });

  describe('Props 测试', () => {
    it('TC04 - 应正确处理默认props', () => {
      const wrapper = shallowMount(NotAllowedEntry);

      // 验证默认props
      expect(wrapper.props('viewData')).toEqual({});
      expect(wrapper.props('parameter')).toEqual({});
    });

    it('TC05 - 应正确接收viewData和parameter props', () => {
      const viewData = createViewData();
      const parameter = createParameter('2');
      const wrapper = shallowMount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      expect(wrapper.props('viewData')).toEqual(viewData);
      expect(wrapper.props('parameter')).toEqual(parameter);
    });
  });

  describe('类型2渲染测试', () => {
    it('TC06 - 当parameter.type为"2"时应显示产品信息表格', () => {
      const viewData = createViewData();
      const parameter = createParameter('2');
      const wrapper = mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证显示产品信息相关字段
      expect(wrapper.text()).toContain('产品名称');
      expect(wrapper.text()).toContain('品牌');
      expect(wrapper.text()).toContain('数量/重量');
      expect(wrapper.text()).toContain('原产地');
      expect(wrapper.text()).toContain('进口商名称');
      expect(wrapper.text()).toContain('进境口岸');
      expect(wrapper.text()).toContain('处置措施');
      expect(wrapper.text()).toContain('不合格原因');
      expect(wrapper.text()).toContain('报送时间');
      expect(wrapper.text()).toContain('附件');

      // 验证不显示检验检疫相关字段
      expect(wrapper.text()).not.toContain('HS编码');
      expect(wrapper.text()).not.toContain('检验检疫编号');
      expect(wrapper.text()).not.toContain('生产企业信息');
      expect(wrapper.text()).not.toContain('进口商备案号');
      expect(wrapper.text()).not.toContain('未准入境的事实');
    });

    it('TC07 - 类型2应正确显示所有字段数据', () => {
      const viewData = createViewData();
      const parameter = createParameter('2');
      const wrapper = mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证数据显示
      expect(wrapper.text()).toContain(viewData.ProductName);
      expect(wrapper.text()).toContain(viewData.TradeMark);
      expect(wrapper.text()).toContain(viewData.Weight.toString());
      expect(wrapper.text()).toContain(viewData.Origin);
      expect(wrapper.text()).toContain(viewData.ImporterName);
      expect(wrapper.text()).toContain(viewData.Entryport);
      expect(wrapper.text()).toContain(viewData.Disposemeasure);
      expect(wrapper.text()).toContain(viewData.RefusedReason);
      expect(wrapper.text()).toContain(viewData.SubmissionMonth);
    });

    it('TC08 - 类型2应正确显示附件链接', () => {
      const viewData = createViewData();
      const parameter = createParameter('2');
      const wrapper = mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证附件链接存在
      const attachmentLink = wrapper.find('a[target="_blank"]');
      expect(attachmentLink.exists()).toBe(true);
      expect(attachmentLink.text()).toBe('查看详情');
      expect(attachmentLink.attributes('href')).toBe(viewData.Attachment);
    });

    it('TC09 - 类型2当附件为空时应显示占位符', () => {
      const viewData = createViewData({ Attachment: null });
      const parameter = createParameter('2');
      const wrapper = mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证没有附件链接
      const attachmentLink = wrapper.find('a[target="_blank"]');
      expect(attachmentLink.exists()).toBe(false);

      // 验证显示占位符
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('默认类型渲染测试', () => {
    it('TC10 - 当parameter.type不为"2"时应显示检验检疫表格', () => {
      const viewData = createViewData();
      const parameter = createParameter('1');
      const wrapper = mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证显示检验检疫相关字段
      expect(wrapper.text()).toContain('HS编码');
      expect(wrapper.text()).toContain('检验检疫编号');
      expect(wrapper.text()).toContain('产品名称');
      expect(wrapper.text()).toContain('产地');
      expect(wrapper.text()).toContain('生产企业信息');
      expect(wrapper.text()).toContain('数量/重量');
      expect(wrapper.text()).toContain('进境口岸');
      expect(wrapper.text()).toContain('进口商备案号');
      expect(wrapper.text()).toContain('未准入境的事实');
      expect(wrapper.text()).toContain('报送时间');
      expect(wrapper.text()).toContain('附件');

      // 验证不显示产品信息特有字段
      expect(wrapper.text()).not.toContain('进口商名称');
      expect(wrapper.text()).not.toContain('处置措施');
      expect(wrapper.text()).not.toContain('不合格原因');
    });

    it('TC11 - 默认类型应正确显示所有字段数据', () => {
      const viewData = createViewData();
      const parameter = createParameter();
      const wrapper = mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证数据显示
      expect(wrapper.text()).toContain(viewData.HsCode);
      expect(wrapper.text()).toContain(viewData.Quarantinecode);
      expect(wrapper.text()).toContain(viewData.ProductName);
      expect(wrapper.text()).toContain(viewData.Origin);
      expect(wrapper.text()).toContain(viewData.TradeMark);
      expect(wrapper.text()).toContain(viewData.Entryport);
      expect(wrapper.text()).toContain(viewData.Eoriimporter);
      expect(wrapper.text()).toContain(viewData.RefusedReason);
      expect(wrapper.text()).toContain(viewData.SubmissionMonth);
    });

    it('TC12 - 默认类型应正确调用numberToHumanWithUnit', () => {
      const viewData = createViewData();
      const parameter = createParameter();
      mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证numberToHumanWithUnit被调用
      expect(numberToHumanWithUnit).toHaveBeenCalledWith(viewData.Weight);
    });

    it('TC13 - 默认类型应正确显示FileLogo组件', () => {
      const viewData = createViewData();
      const parameter = createParameter();
      const wrapper = shallowMount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证FileLogo组件存在
      const fileLogo = wrapper.findComponent(FileLogo);
      expect(fileLogo.exists()).toBe(true);
    });

    it('TC14 - 默认类型当附件为空时应显示占位符', () => {
      const viewData = createViewData({ Attachment: null });
      const parameter = createParameter();
      const wrapper = mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证没有FileLogo组件
      const fileLogo = wrapper.findComponent(FileLogo);
      expect(fileLogo.exists()).toBe(false);

      // 验证显示占位符
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('Setup函数测试', () => {
    it('TC15 - getUrl函数应正确处理已知文件类型', () => {
      const viewData = createViewData();
      const parameter = createParameter();
      const wrapper = shallowMount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      const vm = wrapper.vm as any;

      // 测试已知文件类型
      expect(vm.getUrl('pdf', 'test.pdf')).toBe('pdf');
      expect(vm.getUrl('doc', 'test.doc')).toBe('doc');
    });

    it('TC16 - getUrl函数应正确处理未知文件类型', () => {
      const viewData = createViewData();
      const parameter = createParameter();
      const wrapper = shallowMount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      const vm = wrapper.vm as any;

      // 测试未知文件类型，应该从URL提取
      expect(vm.getUrl('unknown', 'test.pdf')).toBe('pdf');
      expect(vm.getUrl(null, 'test.docx')).toBe('docx');
    });

    it('TC17 - getType函数应正确从URL提取文件扩展名', () => {
      const viewData = createViewData();
      const parameter = createParameter();
      const wrapper = shallowMount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      const vm = wrapper.vm as any;

      // 测试文件扩展名提取
      expect(vm.getType('document.pdf')).toBe('pdf');
      expect(vm.getType('path/to/file.docx')).toBe('docx');
      expect(vm.getType('file.name.with.dots.xlsx')).toBe('xlsx');
    });

    it('TC18 - getType函数应正确处理空URL', () => {
      const viewData = createViewData();
      const parameter = createParameter();
      const wrapper = shallowMount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      const vm = wrapper.vm as any;

      // 测试空URL
      expect(vm.getType(null)).toBe('other');
      expect(vm.getType(undefined)).toBe('other');
      expect(vm.getType('')).toBe('other');
    });

    it('TC19 - goDetail函数应正确调用window.open', () => {
      const viewData = createViewData();
      const parameter = createParameter();
      const wrapper = shallowMount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      const vm = wrapper.vm as any;
      const testUrl = 'https://example.com/test.pdf';

      // 调用goDetail函数
      vm.goDetail(testUrl);

      // 验证window.open被调用
      expect(window.open).toHaveBeenCalledWith(testUrl);
    });

    it('TC20 - goDetail函数应正确处理空URL', () => {
      const viewData = createViewData();
      const parameter = createParameter();
      const wrapper = shallowMount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      const vm = wrapper.vm as any;

      // 调用goDetail函数（空URL）
      vm.goDetail(null);
      vm.goDetail(undefined);
      vm.goDetail('');

      // 验证window.open没有被调用
      expect(window.open).not.toHaveBeenCalled();
    });
  });

  describe('空数据处理测试', () => {
    it('TC21 - 应正确处理所有字段为空的情况', () => {
      const viewData = createViewData({
        ProductName: null,
        TradeMark: null,
        Weight: null,
        Origin: null,
        ImporterName: null,
        Entryport: null,
        Disposemeasure: null,
        RefusedReason: null,
        SubmissionMonth: null,
        Attachment: null,
        AttachType: null,
        HsCode: null,
        Quarantinecode: null,
        Eoriimporter: null,
      });
      const parameter = createParameter('2');
      const wrapper = mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证显示占位符
      const cells = wrapper.findAll('td:not(.tb)');
      const placeholders = cells.wrappers.filter((cell) => cell.text().includes('-'));
      expect(placeholders.length).toBeGreaterThan(0);
    });

    it('TC22 - 应正确处理Weight为0的情况', () => {
      const viewData = createViewData({ Weight: 0 });
      const parameter = createParameter();
      mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证numberToHumanWithUnit被调用
      expect(numberToHumanWithUnit).toHaveBeenCalledWith(0);
    });
  });

  describe('表格结构测试', () => {
    it('TC23 - 应正确设置表格单元格的宽度和colspan', () => {
      const viewData = createViewData();
      const parameter = createParameter('2');
      const wrapper = mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证宽度设置
      const widthCells = wrapper.findAll('th[width="23%"], td[width="27%"]');
      expect(widthCells.length).toBeGreaterThan(0);

      // 验证colspan设置
      const colspanCells = wrapper.findAll('td[colspan="3"]');
      expect(colspanCells.length).toBeGreaterThan(0);
    });

    it('TC24 - 应正确设置tb类名', () => {
      const viewData = createViewData();
      const parameter = createParameter();
      const wrapper = mount(NotAllowedEntry, {
        propsData: { viewData, parameter },
      });

      // 验证tb类名
      const tbCells = wrapper.findAll('th.tb');
      expect(tbCells.length).toBeGreaterThan(0);
    });
  });
});
