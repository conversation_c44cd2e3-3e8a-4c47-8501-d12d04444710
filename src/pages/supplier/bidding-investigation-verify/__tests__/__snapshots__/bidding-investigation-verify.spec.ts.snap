// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`BiddingInvestigationVerify > render 1`] = `
<div style="padding-top: 40px;">
  <div class="breadcrumb">
    <div class="ant-breadcrumb"><span class=""><span class="ant-breadcrumb-link"><a><q-icon-stub type="icon-mianbaoxiefanhui"></q-icon-stub>招标排查</a></span><span class="ant-breadcrumb-separator">/</span></span><span class=""><span class="ant-breadcrumb-link">资质筛查</span><span class="ant-breadcrumb-separator">/</span></span></div>
  </div>
  <div class="root">
    <div class="header">
      <div class="wrapper border">
        <div class="title"><span>当前匹配成功<span style="color: #F04040;"> 0 </span>家企业</span></div>
        <div class="extra">
          <div>
            <wave-stub><button disabled="disabled" type="button" class="ant-btn ant-btn-primary">
                <aicon-stub type="plus-circle"></aicon-stub><span>添加企业</span>
              </button></wave-stub>
          </div>
        </div>
      </div>
    </div>
    <div class="body" style="padding: 15px 15px 0px; min-height: calc(100vh - 162px); display: flex; flex-direction: column;">
      <div style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
        <div class="container" bordered="true">
          <div class="ant-spin-nested-loading">
            <div>
              <div class="ant-spin ant-spin-spinning"><span class="ant-spin-dot ant-spin-dot-spin"><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i></span></div>
            </div>
            <div class="ant-spin-container ant-spin-blur">
              <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
                <div class="ant-spin-nested-loading">
                  <div class="ant-spin-container">
                    <div class="ant-table ant-table-fixed-header ant-table-scroll-position-left ant-table-layout-fixed ant-table-default ant-table-bordered ant-table-empty">
                      <div class="ant-table-content">
                        <div class="ant-table-scroll">
                          <div class="ant-table-header">
                            <table class="ant-table-fixed" style="width: 1680px;">
                              <colgroup>
                                <col style="width: 58px; min-width: 58px;">
                                <col>
                                <col style="width: 88px; min-width: 88px;">
                                <col style="width: 180px; min-width: 180px;">
                                <col style="width: 100px; min-width: 100px;">
                                <col style="width: 180px; min-width: 180px;">
                                <col style="width: 180px; min-width: 180px;">
                                <col style="width: 100px; min-width: 100px;">
                                <col style="width: 100px; min-width: 100px;">
                                <col style="width: 200px; min-width: 200px;">
                                <col style="width: 80px; min-width: 80px;">
                              </colgroup>
                              <thead class="ant-table-thead">
                                <tr>
                                  <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                          </div></span></th>
                          <th key="1" align="left" class=""><span class="ant-table-header-column"><div><span class="ant-table-column-title">企业名称</span><span class="ant-table-column-sorter"></span>
                        </div></span></th>
                        <th key="2" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">登记状态</span><span class="ant-table-column-sorter"></span>
                      </div></span></th>
                      <th key="creditcode" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">统一社会信用代码</span><span class="ant-table-column-sorter"></span>
                    </div></span></th>
                    <th key="4" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">法定代表人</span><span class="ant-table-column-sorter"></span>
                  </div></span></th>
                  <th key="registcapi" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">注册资本</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                <th key="reccap" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">实缴资本</span><span class="ant-table-column-sorter"></span>
              </div></span></th>
              <th key="insuredcount" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">参保人数</span><span class="ant-table-column-sorter"></span>
            </div></span></th>
            <th key="8" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">成立日期</span><span class="ant-table-column-sorter"></span>
          </div></span></th>
          <th key="scope" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">经营范围</span><span class="ant-table-column-sorter"></span>
        </div></span></th>
        <th key="10" align="left" class="ant-table-fixed-columns-in-body ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">操作</span><span class="ant-table-column-sorter"></span>
      </div></span></th>
      </tr>
      </thead>
      </table>
    </div>
    <div tabindex="-1" class="ant-table-body" style="overflow-x: scroll; max-height: calc(100vh - 328px); overflow-y: scroll;">
      <table class="ant-table-fixed" style="width: 1680px;">
        <colgroup>
          <col style="width: 58px; min-width: 58px;">
          <col>
          <col style="width: 88px; min-width: 88px;">
          <col style="width: 180px; min-width: 180px;">
          <col style="width: 100px; min-width: 100px;">
          <col style="width: 180px; min-width: 180px;">
          <col style="width: 180px; min-width: 180px;">
          <col style="width: 100px; min-width: 100px;">
          <col style="width: 100px; min-width: 100px;">
          <col style="width: 200px; min-width: 200px;">
          <col style="width: 80px; min-width: 80px;">
        </colgroup>
        <tbody class="ant-table-tbody"></tbody>
      </table>
    </div>
    <div class="ant-table-placeholder">
      <div class="empty"></div>
    </div>
  </div>
  <div class="ant-table-fixed-right">
    <div class="ant-table-header">
      <table class="ant-table-fixed" style="width: 80px;">
        <colgroup>
          <col style="width: 80px; min-width: 80px;">
        </colgroup>
        <thead class="ant-table-thead">
          <tr>
            <th key="10" align="left" class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">操作</span><span class="ant-table-column-sorter"></span>
    </div></span></th>
    </tr>
    </thead>
    </table>
  </div>
  <div class="ant-table-body-outer">
    <div class="ant-table-body-inner" style="max-height: calc(100vh - 328px); overflow-y: scroll;">
      <table class="ant-table-fixed" style="width: 80px;">
        <colgroup>
          <col style="width: 80px; min-width: 80px;">
        </colgroup>
        <tbody class="ant-table-tbody"></tbody>
      </table>
    </div>
  </div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div class="formWrapper">
  <form class="ant-form ant-form-inline form flex">
    <div class="flex items-center" style="width: 100%; gap: 20px;">
      <div style="width: 100%;">
        <div class="flex" style="gap: 20px;">
          <aformitem-stub label="项目名称" required="true" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
            <anonymous-stub prefixcls="ant-input" inputtype="input" value="" allowclear="true" element="[object Object]" handlereset="[Function]" class=""></anonymous-stub>
          </aformitem-stub>
          <aformitem-stub label="项目编号" required="true" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
            <anonymous-stub prefixcls="ant-input" inputtype="input" value="" allowclear="true" element="[object Object]" handlereset="[Function]" class=""></anonymous-stub>
          </aformitem-stub>
        </div>
        <div class="flex" style="gap: 20px; display: none;">
          <aformitem-stub label=" 资质信息" labelcol="[object Object]" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
            <select-stub defaultactivefirstoption="true" multiple="true" prefixcls="ant-select" transitionname="slide-up" optionlabelprop="children" optionfilterprop="value" choicetransitionname="zoom" placeholder="请选择资格证书" value="" dropdownstyle="[object Object]" maxtagcount="3" tokenseparators="" showaction="click" clearicon="[object Object]" inputicon="[object Object]" removeicon="[object Object]" menuitemselectedicon="[object Object]" dropdownrender="[Function]" dropdownmatchselectwidth="true" dropdownmenustyle="[object Object]" notfoundcontent="[object Object]" tabindex="0" autoclearsearchvalue="true" __propssymbol__="Symbol()" children="[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object]" class=""></select-stub>
          </aformitem-stub>
          <aformitem-stub label="" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"><label class="ant-checkbox-wrapper">
              <checkbox-stub prefixcls="ant-checkbox" type="checkbox" class=""></checkbox-stub><span>必须具备</span>
            </label></aformitem-stub>
        </div>
      </div>
      <aformitem-stub>
        <wave-stub><button disabled="disabled" type="button" class="ant-btn ant-btn-primary"><span>开始排查</span></button></wave-stub>
      </aformitem-stub>
    </div>
  </form>
</div>
</div>
</div>
</div>
`;
