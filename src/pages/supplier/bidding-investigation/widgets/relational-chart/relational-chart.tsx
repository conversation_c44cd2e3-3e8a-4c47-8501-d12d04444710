import {
  defineComponent,
  ref,
  shallowRef,
  unref,
  onMounted,
  onUnmounted,
  getCurrentInstance,
  shallowReactive,
  watch,
  type PropType,
} from 'vue';
import { useDebounceFn, useFullscreen } from '@vueuse/core';
import { isObject, uniq } from 'lodash';

import QIcon from '@/components/global/q-icon';
import { downloadScreenshot } from '@/utils/screenshot';
import { preDelDetailData } from '@/components/relational-path/use-data-source-hook';

import { createRelationshipChart, NetworkNode, NetworkEdge, CHART_DEFAULT_ZOOM_LEVEL } from './chart';
import styles from './relational-chart.module.less';
import openSameInfoDrawer from '../relational-table-view/same-info-drawer';
import { TableMap } from '../../config';
import Legend from './relational-chart-legend';
import Copyright from './relational-chart-copyright';

const createPopper = (instance, el, chartNodes, activeBiddingRelation, options?) => {
  const popoverList = [];
  const edges = instance?.edges?.() || [];
  edges.forEach((ele) => {
    const popover = ele?.popper({
      content: (e) => {
        // 获取链路数据
        const { data: lineData } = e?.data() || {};
        const phoneData = lineData || [];
        const label = e?.data('label');

        const TypeArr = Object.entries(TableMap).map(([key, value]) => {
          return {
            key,
            ...value,
          };
        });

        // 构建类型映射
        const typeMap = {
          相同电话号码: 'ContactNumber',
          相同经营地址: 'Address',
          相同邮箱: 'Mail',
        };

        // 创建弹出层DOM
        const div = document.createElement('div');
        const fnMap = {};
        e?.data('label', '');

        // 判断是否为链接
        const isLink = (str) => TypeArr.find((v) => v.title?.includes(str));

        // 构建内容HTML
        const buildContent = (item) => {
          if (isLink(item.role)) {
            return `<span class="span-link">${item.role}</span>`;
          }

          let title = uniq(!item.data.some(isObject) ? item.data : item.data?.map((i) => i.address))?.join(', ');

          // 处理特殊类型数据
          if (['ContactNumber', 'Address', 'HisEmploy', 'HisLegal', 'HisInvest', 'Mail'].includes(item.type)) {
            const data =
              {
                Address: item.data,
                ContactNumber: phoneData.find((p) => p.type === 'ContactNumber')?.originalData,
                Mail: item.rawData ?? item,
              }[item.type] || item.data;

            const { isShowMore, handleShowMore } = preDelDetailData(
              data,
              chartNodes.map((node) => node.data)
            );

            fnMap[item.type] = handleShowMore;

            if (isShowMore(item.type)) {
              title += '（查看更多）';
              e.data().clickFn = (type) => fnMap[type]?.(type, item);
            }
          }

          if (!title) {
            return `<span class="tips" style="gap: 5px;">${item.role}</span>`;
          }

          return `<span class="tips" style="gap: 5px;" tip="${title}">${item.role}</span>`;
        };

        div.innerHTML = `
          <div style="background-color:rgba(255, 255, 255, 0.75); color: #999; font-size: 12px; padding: 0 5px; border-radius: 3px;">
            ${
              e?.data('data')?.map(buildContent).join(', ') ||
              `${label?.split(',').map((v) => `<span class="${isLink(v) ? 'span-link' : ''}">${v}</span>`)}`
            }
          </div>
        `;

        // 绑定点击事件
        div.addEventListener('click', (event) => {
          const data = e.data();
          const typeName = (event.target as HTMLDivElement).innerText;
          const type = TypeArr.find((v) => v.title?.includes(typeName))?.key;
          const nodes = instance.nodes().map((n) => n.data());

          // 构建基础记录
          let record: any = {
            startCompanyKeyno: data.source,
            endCompanyKeyno: data.target,
            startCompanyName: nodes?.find((n) => n.id === data.source)?.label,
            endCompanyName: nodes?.find((n) => n.id === data.target)?.label,
          };

          // 处理控制关系
          if (type === 'ControlRelation') {
            const params = {
              keyNoAndNames: [
                { companyId: record.startCompanyKeyno, companyName: record.startCompanyName },
                { companyId: record.endCompanyKeyno, companyName: record.endCompanyName },
              ],
            };
            options?.openRelationModal(params, type);
          }
          // 处理担保人
          else if (type && nodes.length === 3 && type === 'Guarantor') {
            record = {
              ...record,
              startCompanyKeyno: nodes[0]?.id,
              endCompanyKeyno: nodes[2]?.id,
              startCompanyName: nodes[0]?.label,
              endCompanyName: nodes[2]?.label,
              drawerTitleList: [
                { companyId: record.startCompanyKeyno, companyName: record.startCompanyName },
                { companyId: record.endCompanyKeyno, companyName: record.endCompanyName },
              ],
            };
            openSameInfoDrawer({ record, type });
          }
          // 处理其他类型
          else if (type) {
            openSameInfoDrawer({ record, type });
          }

          // 处理查看更多
          if (
            data.clickFn &&
            (Object.keys(typeMap).includes(typeName) || typeName.includes('历史')) &&
            (event.target as any)?.getAttribute('tip')?.includes('查看更多')
          ) {
            const t = typeName.includes('历史') ? data.data.find((item) => item.role === typeName)?.type : typeMap[typeName];
            data.clickFn?.(t);
          }
        });

        el.appendChild(div);
        return div;
      },
      popper: {
        placement: 'bottom',
        modifiers: [
          {
            name: 'flip',
            enabled: false,
          },
          {
            name: 'offset',
            options: {
              offset: ({ placement, reference, popper }) => {
                return placement === 'bottom' ? [0, -popper.height / 1.5] : [];
              },
            },
          },
        ],
      },
    });

    const update = () => {
      popover?.update();
    };
    ele?.connectedNodes()?.on('position', update);
    instance.on('pan zoom resize', update);
    popoverList.push(popover);
  });
  return popoverList;
};

const RelationalChart = defineComponent({
  name: 'RelationalChart',
  props: {
    dataSource: {
      type: Object as PropType<{
        nodes: NetworkNode[];
        edges: NetworkEdge[];
      }>,
      default: () => ({}),
    },
    activeBiddingRelation: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const containerRef = ref<HTMLElement | null>(null);
    const chartRef = ref<HTMLElement | null>(null);
    const chartInstance = shallowRef<any>(null);
    const vm = getCurrentInstance();

    let popoverList = [];

    const destroyChart = () => {
      popoverList.forEach((popover) => {
        popover?.destroy();
      });
      popoverList = [];
      chartInstance.value?.destroy();
    };

    const openRelationModal = (record, type) => {
      const ins = vm.proxy;

      ins.$modal.showDimension(type, record);
    };

    /**
     * 初始化图表
     */
    const initChart = () => {
      // 创建前清空上一次节点的内容
      destroyChart();
      // 创建 Chart
      const instance = createRelationshipChart(chartRef.value, props.dataSource, {
        on: {
          click(event) {
            if (event.target.isNode?.()) {
              const type = event.target.data('type');
              const keyNo = event.target.data('id');
              const name = event.target.data('label');
              // 是否是真实id,存在人员keyno为null的情况，但是为了页面显示正常，前端手动赋值，只能针对一个人keyno不存在的情况
              const isTrueId = event.target.data('isTrueId');
              if (keyNo && type === 'company') {
                window.open(`/embed/companyDetail?keyNo=${keyNo}&title=${name}`);
              }
              if (keyNo && type === 'person' && isTrueId) {
                window.open(`/embed/beneficaryDetail?personId=${keyNo}&title=${name}`);
              }
            }
          },
        },
      });
      // 创建 Popper
      popoverList = createPopper(instance, chartRef.value, props.dataSource.nodes, props.activeBiddingRelation, {
        openRelationModal,
      });

      chartInstance.value = instance;
    };

    watch(
      () => props.dataSource,
      (val) => {
        if (!val) {
          chartInstance.value?.destroy();
          return;
        }
        // 延迟初始化图表
        setTimeout(initChart, 80);
      },
      {
        immediate: true,
      }
    );

    onUnmounted(() => {
      destroyChart();
    });

    /**
     * 全屏显示控制
     */
    const { isFullscreen, toggle, exit } = useFullscreen(containerRef);

    /**
     * 重置
     */
    const resize = async () => {
      const instance = chartInstance.value;
      if (!instance) {
        return;
      }

      instance.resize();
      instance.zoom(CHART_DEFAULT_ZOOM_LEVEL);
      instance.center();
    };
    /**
     * 从全屏状态恢复后图谱居中
     */
    watch(
      () => isFullscreen.value,
      () => {
        resize();
      }
    );

    /**
     * 导出图片
     */
    const save = () => {
      const instance = unref(chartInstance);
      instance.fit(50);
      setTimeout(() => {
        const chartEl = unref(chartRef);
        if (chartEl?.parentElement) {
          downloadScreenshot(chartEl.parentElement);
        }
      }, 50);
    };

    /**
     * 窗口改变后重绘图表
     */
    const size = shallowReactive({
      width: '100%',
      height: '100%',
    });

    const getContainerSize = () => {
      const { clientWidth, clientHeight } = vm?.proxy.$el?.parentElement ?? {};
      // 解决外部容器宽高可能为 0 的问题 (非激活状态时)
      if (clientWidth) {
        size.width = `${clientWidth}px`;
      }
      if (clientHeight) {
        size.height = `${clientHeight}px`;
      }
    };
    const redraw = useDebounceFn(() => {
      getContainerSize();
    }, 200);

    onMounted(() => {
      window.addEventListener('resize', redraw);
    });

    onUnmounted(() => {
      window.removeEventListener('resize', redraw);
    });

    return {
      chart: chartRef,
      container: containerRef,
      resize,
      save,
      isFullscreen,
      toggle,
      exit,
      size,
    };
  },
  render() {
    return (
      <div
        class={styles.container}
        ref="container"
        style={{
          width: this.size.width,
          height: this.size.height,
        }}
      >
        <div class={styles.toolbar} data-capture="exclude">
          <div class={styles.item} onClick={this.resize}>
            <i class={styles.icon}>
              <QIcon type="icon-icon_sqq" />
            </i>
            <span class={styles.label}>刷新</span>
          </div>
          <div class={styles.item} onClick={this.toggle}>
            <i class={styles.icon}>
              <QIcon type={this.isFullscreen ? 'icon-tuichu' : 'icon-quanping1'} />
            </i>
            <span class={styles.label}>{this.isFullscreen ? '退出' : '全屏'}</span>
          </div>
          <div class={styles.item} onClick={this.save}>
            <i class={styles.icon}>
              <QIcon type="icon-xiazai" />
            </i>
            <span class={styles.label}>保存</span>
          </div>
        </div>

        <div class={styles.canvas}>
          <Legend float={true} fullscreen={this.isFullscreen} />
          <Copyright float={true} fullscreen={this.isFullscreen} />
          <div class={styles.chart} ref="chart"></div>
        </div>
      </div>
    );
  },
});

export default RelationalChart;
