import { shallowMount } from '@vue/test-utils';

import BiddingInvestigationVerify from '..';
import { biddings } from '@/shared/services';

vi.mock('@/shared/services');

describe('BiddingInvestigationVerify', () => {
  beforeEach(() => {
    vi.mocked(biddings).getCompanyList.mockResolvedValue({
      Result: [],
    });
  });
  afterEach(() => {
    vi.resetAllMocks();
  });
  test('render', () => {
    const wrapper = shallowMount(BiddingInvestigationVerify);
    expect(wrapper).toMatchSnapshot();
  });
});
