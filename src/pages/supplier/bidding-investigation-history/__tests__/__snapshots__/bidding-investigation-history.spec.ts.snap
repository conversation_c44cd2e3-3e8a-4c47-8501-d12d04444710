// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`BiddingInvestigationHistory > render 1`] = `
<hero-layout-stub loading="true">
  <div class="root">
    <div class="body" style="padding-top: 0px;">
      <div class="container">
        <div class="main">
          <div class="root offset q-filter__root">
            <div class="group block q-filter-group--filters q-filter-group lastGroup">
              <div class="label q-filter-label" style="width: auto; padding-top: 0px;">筛选条件</div>
              <div class="wrap wrapGroups q-filter-wrap--filters">
                <div class="flex items-center flex-wrap" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
                  <q-select multiple="true" options="[object Object],[object Object],[object Object],[object Object]" class="select"></q-select>
                  <q-select multiple="true" options="" class="select"></q-select>
                  <q-select value="[object Object]" options="[object Object],[object Object],[object Object],[object Object],[object Object],[object Object]" custom="[object Object]" class="select"></q-select>
                  <div style="display: inline-block;">
                    <div class="container tiny-search__container">
                      <div class="default tiny-search__default">
                        <div class="input">
                          <q-icon-stub type="icon-sousuo"></q-icon-stub><span>搜索</span>
                        </div>
                      </div>
                      <div class="search" style="width: 450px; display: none;">
                        <div class="inputWrapper">
                          <ainput-stub prefixcls="ant-input" placeholder="支持按照项目名称、项目编号、排查编号、企业名称查询" type="text" addonafter="[object Object]" allowclear="true" lazy="true" classname="ant-input-search ant-input-search-enter-button" class="input tiny-search__input"></ainput-stub>
                        </div>
                        <div class="clear" style="display: none;"><a class="tiny-search__cancel">取消</a></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex items-center">
                  <div style="display: inline-block;">
                    <div class="aside"><button type="button" class="ant-btn ant-btn-link">
                        <q-icon-stub type="icon-chexiaozhongzhi"></q-icon-stub><span>重置筛选</span>
                      </button></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="root" scroll="[object Object]" showindex="true" isfixed="true">
    <div class="header">
      <div class="wrapper border">
        <div class="title">
          <div class="container"><span>共找到<localereceiver-stub componentname="Icon"><i aria-label="undefined: sync" class="anticon anticon-sync"><antdicon-stub type="sync-o" focusable="false" class="anticon-spin"></antdicon-stub></i></localereceiver-stub><em style="display: none;">0</em>条相关结果</span></div>
        </div>
        <div class="extra">
          <div>
            <div>
              <!---->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="body" style="padding: 15px;">
      <div class="container">
        <div class="ant-spin-nested-loading">
          <div>
            <div class="ant-spin ant-spin-spinning"><span class="ant-spin-dot ant-spin-dot-spin"><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i></span></div>
          </div>
          <div class="ant-spin-container ant-spin-blur">
            <div class="ant-table-wrapper table scrollContentSet" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
              <div class="ant-spin-nested-loading">
                <div class="ant-spin-container">
                  <div class="ant-table ant-table-fixed-header ant-table-scroll-position-left ant-table-layout-fixed ant-table-default ant-table-bordered ant-table-empty">
                    <div class="ant-table-content">
                      <div class="ant-table-scroll">
                        <div class="ant-table-header">
                          <table class="ant-table-fixed" style="width: auto;">
                            <colgroup>
                              <col class="ant-table-selection-col">
                              <col style="width: 58px; min-width: 58px;">
                              <col style="width: 200px; min-width: 200px;">
                              <col style="width: 140px; min-width: 140px;">
                              <col style="width: 200px; min-width: 200px;">
                              <col style="width: 140px; min-width: 140px;">
                              <col style="width: 125px; min-width: 125px;">
                              <col style="width: 100px; min-width: 100px;">
                              <col style="width: 100px; min-width: 100px;">
                              <col style="width: 110px; min-width: 110px;">
                              <col style="width: 100px; min-width: 100px;">
                              <col style="width: 88px; min-width: 88px;">
                              <col style="width: 100px; min-width: 100px;">
                              <col style="width: 105px; min-width: 105px;">
                              <col style="width: 150px; min-width: 150px;">
                              <col style="width: 80px; min-width: 80px;">
                            </colgroup>
                            <thead class="ant-table-thead">
                              <tr>
                                <th key="selection-column" align="left" class="ant-table-fixed-columns-in-body ant-table-selection-column"><span class="ant-table-header-column"><div><span class="ant-table-column-title"><div class="ant-table-selection"><label class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled"><span class="ant-checkbox ant-checkbox-disabled"><input type="checkbox" disabled="disabled" class="ant-checkbox-input" value=""><span class="ant-checkbox-inner"></span></span></label>
                        </div></span><span class="ant-table-column-sorter"></span>
                      </div></span></th>
                      <th key="1" align="left" class="ant-table-fixed-columns-in-body ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                    </div></span></th>
                    <th key="2" align="left" class="ant-table-fixed-columns-in-body ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">项目名称</span><span class="ant-table-column-sorter"></span>
                  </div></span></th>
                  <th key="projectNo" align="left" class="ant-table-fixed-columns-in-body ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">项目编号</span><span class="ant-table-column-sorter"></span>
                </div></span></th>
                <th key="4" align="left" class="ant-table-fixed-columns-in-body ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">排查主体</span><span class="ant-table-column-sorter"></span>
              </div></span></th>
              <th key="tenderNo" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">排查编号</span><span class="ant-table-column-sorter"></span>
            </div></span></th>
            <th key="result" align="left" class="ant-table-column-has-actions ant-table-column-has-sorters ant-table-row-cell-break-word"><span class="ant-table-header-column"><div class="ant-table-column-sorters"><span class="ant-table-column-title">排查结果</span><span class="ant-table-column-sorter"><div title="Sort" class="ant-table-column-sorter-inner ant-table-column-sorter-inner-full"><i aria-label="icon: caret-up" class="anticon anticon-caret-up ant-table-column-sorter-up off"><svg viewBox="0 0 1024 1024" data-icon="caret-up" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"></path></svg></i><i aria-label="icon: caret-down" class="anticon anticon-caret-down ant-table-column-sorter-down off"><svg viewBox="0 0 1024 1024" data-icon="caret-down" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"></path></svg></i></div></span>
          </div></span></th>
          <th key="BiddingCompanyCertification" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">资质筛查</span><span class="ant-table-column-sorter"></span>
        </div></span></th>
        <th key="BiddingCompanyRelation" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">深度关系排查</span><span class="ant-table-column-sorter"></span>
      </div></span></th>
      <th key="PurchaseIllegal" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">涉采购不良行为</span><span class="ant-table-column-sorter"></span>
    </div></span></th>
    <th key="JointBiddingAnalysis" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">共同投标分析</span><span class="ant-table-column-sorter"></span>
  </div></span></th>
  <th key="risk_inner_blacklist" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">内部黑名单</span><span class="ant-table-column-sorter"></span></div></span></th>
  <th key="risk_interest_conflict" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">潜在利益冲突</span><span class="ant-table-column-sorter"></span></div></span></th>
  <th key="editor.name" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">操作人</span><span class="ant-table-column-sorter"></span></div></span></th>
  <th key="createDate" align="left" class="ant-table-column-has-actions ant-table-column-has-sorters ant-table-row-cell-break-word"><span class="ant-table-header-column"><div class="ant-table-column-sorters"><span class="ant-table-column-title">排查时间</span><span class="ant-table-column-sorter"><div title="Sort" class="ant-table-column-sorter-inner ant-table-column-sorter-inner-full"><i aria-label="icon: caret-up" class="anticon anticon-caret-up ant-table-column-sorter-up off"><svg viewBox="0 0 1024 1024" data-icon="caret-up" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"></path></svg></i><i aria-label="icon: caret-down" class="anticon anticon-caret-down ant-table-column-sorter-down off"><svg viewBox="0 0 1024 1024" data-icon="caret-down" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"></path></svg></i></div></span></div></span></th>
  <th key="15" align="left" class="ant-table-fixed-columns-in-body ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">操作</span><span class="ant-table-column-sorter"></span></div></span></th>
  </tr>
  </thead>
  </table>
  </div>
  <div tabindex="-1" class="ant-table-body" style="overflow-x: scroll; max-height: calc(100vh - 240px - 0px); overflow-y: scroll;">
    <table class="ant-table-fixed" style="width: max-content;">
      <colgroup>
        <col class="ant-table-selection-col">
        <col style="width: 58px; min-width: 58px;">
        <col style="width: 200px; min-width: 200px;">
        <col style="width: 140px; min-width: 140px;">
        <col style="width: 200px; min-width: 200px;">
        <col style="width: 140px; min-width: 140px;">
        <col style="width: 125px; min-width: 125px;">
        <col style="width: 100px; min-width: 100px;">
        <col style="width: 100px; min-width: 100px;">
        <col style="width: 110px; min-width: 110px;">
        <col style="width: 100px; min-width: 100px;">
        <col style="width: 88px; min-width: 88px;">
        <col style="width: 100px; min-width: 100px;">
        <col style="width: 105px; min-width: 105px;">
        <col style="width: 150px; min-width: 150px;">
        <col style="width: 80px; min-width: 80px;">
      </colgroup>
      <tbody class="ant-table-tbody"></tbody>
    </table>
  </div>
  <div class="ant-table-placeholder">
    <div class="empty"></div>
  </div>
  </div>
  <div class="ant-table-fixed-left">
    <div class="ant-table-header">
      <table class="ant-table-fixed">
        <colgroup>
          <col class="ant-table-selection-col">
          <col style="width: 58px; min-width: 58px;">
          <col style="width: 200px; min-width: 200px;">
          <col style="width: 140px; min-width: 140px;">
          <col style="width: 200px; min-width: 200px;">
        </colgroup>
        <thead class="ant-table-thead">
          <tr>
            <th key="selection-column" align="left" class="ant-table-selection-column"><span class="ant-table-header-column"><div><span class="ant-table-column-title"><div class="ant-table-selection"><label class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled"><span class="ant-checkbox ant-checkbox-disabled"><input type="checkbox" disabled="disabled" class="ant-checkbox-input" value=""><span class="ant-checkbox-inner"></span></span></label>
    </div></span><span class="ant-table-column-sorter"></span>
  </div></span></th>
  <th key="1" align="left" class="ant-table-align-left ant-table-row-cell-break-word" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span></div></span></th>
  <th key="2" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">项目名称</span><span class="ant-table-column-sorter"></span></div></span></th>
  <th key="projectNo" align="left" class="ant-table-row-cell-break-word"><span class="ant-table-header-column"><div><span class="ant-table-column-title">项目编号</span><span class="ant-table-column-sorter"></span></div></span></th>
  <th key="4" align="left" class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">排查主体</span><span class="ant-table-column-sorter"></span></div></span></th>
  </tr>
  </thead>
  </table>
  </div>
  <div class="ant-table-body-outer">
    <div class="ant-table-body-inner" style="max-height: calc(100vh - 240px - 0px); overflow-y: scroll;">
      <table class="ant-table-fixed">
        <colgroup>
          <col class="ant-table-selection-col">
          <col style="width: 58px; min-width: 58px;">
          <col style="width: 200px; min-width: 200px;">
          <col style="width: 140px; min-width: 140px;">
          <col style="width: 200px; min-width: 200px;">
        </colgroup>
        <tbody class="ant-table-tbody"></tbody>
      </table>
    </div>
  </div>
  </div>
  <div class="ant-table-fixed-right">
    <div class="ant-table-header">
      <table class="ant-table-fixed" style="width: 80px;">
        <colgroup>
          <col style="width: 80px; min-width: 80px;">
        </colgroup>
        <thead class="ant-table-thead">
          <tr>
            <th key="15" align="left" class="ant-table-row-cell-break-word ant-table-row-cell-last"><span class="ant-table-header-column"><div><span class="ant-table-column-title">操作</span><span class="ant-table-column-sorter"></span>
    </div></span></th>
    </tr>
    </thead>
    </table>
  </div>
  <div class="ant-table-body-outer">
    <div class="ant-table-body-inner" style="max-height: calc(100vh - 240px - 0px); overflow-y: scroll;">
      <table class="ant-table-fixed" style="width: 80px;">
        <colgroup>
          <col style="width: 80px; min-width: 80px;">
        </colgroup>
        <tbody class="ant-table-tbody"></tbody>
      </table>
    </div>
  </div>
  </div>
  </div>
  </div>
  </div>
  </div>
  </div>
  </div>
  </div>
  </div>
  </div>
  </div>
</hero-layout-stub>
`;
