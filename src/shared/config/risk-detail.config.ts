import { isFunction, isString } from 'lodash';

/**
 * Detail type alias map
 */
export const DETAIL_TYPES_MAP = {
  BusinessAbnormal2: 'jyzx', // 简易注销
  BusinessAbnormal5: 'adminpenaltydetail',
  FreezeEquity: 'assistance',
  ChattelMortgage: 'mPledge', // 动产抵押
  LandMortgage: 'landmortgage', // 土地抵押
  EquityPledge: 'pledge',
  JudicialAuction: 'sfpaimaiDetail',
  JudicialAuction1: 'sfpaimaiDetail',
  GuaranteeInfo: 'guarantor', // 担保信息
  GuaranteeRisk: 'wenshuDetail',
  PersonCreditCurrent: 'shixin',
  PersonCreditHistory: 'shixin',
  MainMembersPersonCreditCurrent: 'shixin',
  MainMembersRestrictedConsumptionCurrent: 'xiangaoDetail',
  SubsidiaryPersonCreditCurrent: 'shixin',
  SubsidiaryRestrictedConsumptionCurrent: 'xiangaoDetail',
  RestrictedConsumptionCurrent: 'xiangaoDetail',
  RestrictedConsumptionHistory: 'xiangaoDetail',
  TaxationOffences: 'taxIllegal',
  Bankruptcy: 'bankruptcy',
  PersonExecution: 'zhixing',
  ProductQualityProblem1: 'productrecall',
  ProductQualityProblem2: 'productChecked',
  ProductQualityProblem3: 'adminpenaltydetail',
  ProductQualityProblem4: 'adminpenaltydetail',
  ProductQualityProblem5: 'drc',
  ProductQualityProblem6: 'notallowedentry',
  ProductQualityProblem7: 'medicine', // 药品抽检
  ProductQualityProblem8: 'adminpenaltydetail',
  ProductQualityProblem9: 'foodsafety', // 食品安全检查不合格
  BondDefaults: 'bond',
  AdministrativePenalties: 'adminpenaltydetail',
  EnvironmentalPenalties: 'adminpenaltydetail',
  TaxArrearsNotice: 'owenotice',
  BillDefaults: 'billDefaults',
  HitOuterBlackList: 'govProcurementIllegal',
  CapitalReduction: 'decreaseCapiNotice',
  TaxReminder: 'taxReminder',
  TaxCallNoticeV2: 'taxCallNotice',
  StockPledge: 'stockPledge',
  CancellationOfFiling: 'enliqDetail', // 注销备案详情
  ChattelSeizure: 'chattelSeizure',
  SeparationNotice: 'separationNotice',
  SpotCheck: 'spotCheck', //抽查检查
  NoticeInTimePeriod: 'ktnotice', //开庭公告
  GovProcurementIllegal: 'govProcurementIllegal', //国央企采购黑名单
  EndExecutionCase: 'endExecutionCase', // 终本案件
  LaborContractDispute: 'sameCaseList', // 劳动纠纷
};

const getUrlPath = (urlObj, item) => {
  const { urlType, paramsObj } = urlObj;
  const url = isFunction(urlType) ? urlType(item) : urlType;
  if (!url) {
    return '';
  }
  const params = Object.keys(paramsObj)
    .reduce((r: any[], k: string) => {
      const paramsValueSetting = paramsObj[k];
      // 如果只需要取值的话，直接取值就可以了
      if (isString(paramsValueSetting) || !paramsValueSetting) {
        r.push(`${k}=${item[paramsValueSetting]}`);
      } else {
        const { fieldKey, getValue } = paramsValueSetting;
        const valuesByKey = fieldKey
          .map((key) => item[key])
          .filter(Boolean)
          .map(getValue)
          .filter(Boolean)?.[0];
        r.push(`${k}=${valuesByKey}`);
      }
      return r;
    }, [])
    .join('&');
  return `/embed/${url}?${params}`;
};
export function getDetailByType(key, item) {
  let params = {};
  switch (key) {
    case 'MainMembersRestrictedConsumptionCurrent': // 主要人员限制高消费
    case 'SubsidiaryRestrictedConsumptionCurrent': // 子公司限制高消费
    case 'RestrictedConsumptionCurrent': // 限制高消费（当前有效）
    case 'RestrictedConsumptionHistory': // 历史限制高消费
      params = {
        urlType: 'sumptuary',
        paramsObj: {
          id: 'RiskId',
          title: 'CaseNo',
        },
      };
      break;
    case 'BusinessAbnormal5': // 疑似停业歇业停产或被吊销证照
    case 'ProductQualityProblem3': // 假冒伪劣产品
    case 'ProductQualityProblem4': // 虚假宣传
    case 'ProductQualityProblem8': // 假冒化妆品
    case 'BidCollusive': // 围串标关联（招标排查）
      params = {
        urlType: 'adminpenaltydetail',
        paramsObj: {
          id: {
            fieldKey: ['id', 'Id'],
            getValue: (v) => v,
          },
          title: 'CaseNo',
        },
      };
      break;
    case 'EnvironmentalPenalties': // 环保处罚
    case 'AdministrativePenalties': // 行政处罚
    case 'TaxPenalties': // 税务处罚
    case 'AdministrativePenalties2': // 涉及商业贿赂、垄断行为
    case 'AdministrativePenalties3': // 政府采购活动违法行为行政处罚
    case 'BidAdministrativePenalties': // 涉围串标处罚
      params = {
        urlType: 'adminpenaltydetail',
        paramsObj: {
          id: {
            fieldKey: ['riskid', 'RiskId'],
            getValue: (v) => v,
          },
          title: 'caseno',
        },
      };
      break;
    case 'JudicialAuction': // 司法拍卖
    case 'JudicialAuction1': // 司法拍卖(机器设备)
      params = {
        urlType: 'judicialSale',
        paramsObj: {
          id: {
            fieldKey: ['id', 'Id'],
            getValue: (v) => v,
          },
          title: 'name',
        },
      };
      break;
    case 'ProductQualityProblem1': // 产品召回
      params = {
        urlType: 'recall-product',
        paramsObj: {
          url: {
            fieldKey: ['RiskId'],
            getValue: (v) => v,
          },
          title: 'Title',
        },
      };
      break;
    case 'GuaranteeRisk': // 担保风险
      params = {
        urlType: 'judgementInfo',
        paramsObj: {
          id: {
            fieldKey: ['id', 'Id'],
            getValue: (v) => v,
          },
        },
      };
      break;
    case 'SalesContractDispute': // 买卖合同纠纷
    case 'MajorDispute': // 重大纠纷
    case 'CompanyOrMainMembersCriminalInvolve': // 近3年涉贪污受贿裁判相关提及方
    case 'CompanyOrMainMembersCriminalInvolveHistory': // 涉贪污受贿裁判相关提及方（3年以上及其他）
      params = {
        urlType: 'judgementInfo',
        paramsObj: {
          id: {
            fieldKey: ['id'],
            getValue: (v) => String(v).slice(0, -1),
          },
          title: 'casename',
        },
      };
      break;
    case 'BidAdministrativeJudgement': // 涉诉围串标记录
      params =
        item.dataType === 'case'
          ? {
              urlType: 'courtCaseDetail',
              paramsObj: {
                caseId: {
                  fieldKey: ['id'],
                  getValue: (v) => v,
                },
                title: 'casename',
              },
            }
          : {
              urlType: 'judgementInfo',
              paramsObj: {
                id: {
                  fieldKey: ['id'],
                  getValue: (v) => String(v).slice(0, -1),
                },
                title: 'casename',
              },
            };
      break;
    case 'CompanyOrMainMembersCriminalOffence': // 公司及主要人员涉刑事犯罪（3年内）
    case 'CompanyOrMainMembersCriminalOffenceHistory': // 公司及主要人员涉刑事犯罪（3年以上及其他）
    case 'LaborContractDispute': // 劳动纠纷
      params = {
        urlType: 'courtCaseDetail',
        paramsObj: {
          caseId: {
            fieldKey: ['id', 'Id'],
            getValue: (v) => v,
          },
          title: 'CaseName',
        },
      };
      break;
    case 'SecurityNotice': // 公安通告
      params = {
        urlType: 'news-detail-page',
        paramsObj: {
          newsId: {
            fieldKey: ['id'],
            getValue: (v) => v,
          },
          keyNo: 'keyNo',
          title: 'reason',
        },
      };
      break;
    // 历史失信人存在searchCaseId为空的情况
    case 'PersonCreditHistory':
      params = {
        urlType: item.CaseSearchId ? 'courtCaseDetail' : '',
        paramsObj: {
          caseId: {
            fieldKey: ['CaseSearchId'],
            getValue: (v) => v,
          },
          title: 'CaseNo',
        },
      };
      break;
    case 'IPRPledge': // 商标还是专利
      params = {
        urlType: item.Type === 2 ? 'tmDetail' : 'patentDetail',
        paramsObj: {
          id: {
            fieldKey: ['Id'],
            getValue: (v) => v,
          },
        },
      };
      break;
    default:
      params = {
        urlType: 'courtCaseDetail',
        paramsObj: {
          caseId: {
            fieldKey: ['CaseSearchId', 'Id', 'id'],
            getValue: (v) => v,
          },
          title: {
            fieldKey: ['CaseNo', 'caseno', 'casename', 'CaseName'],
            getValue: (v) => v,
          },
        },
      };
      break;
  }
  const newUrl = getUrlPath(params, item);
  return newUrl;
}
