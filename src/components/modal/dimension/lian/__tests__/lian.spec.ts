import { mount } from '@vue/test-utils';
import LianDimension from '../index.tsx';

describe('LianDimension', () => {
  const defaultViewData = {
    Reason: '合同纠纷',
    CaseReasonDescription: '详细案由描述',
    CaseSearchId: ['case123'],
    CaseNo: '2023民初123号',
    RegistDate: '2023-01-01',
    HoldDate: '2023-02-01',
    FinishDate: '2023-03-01',
    Department: '民事审判庭',
    Court: '北京市第一中级人民法院',
    Judger: '张三',
    Assistant: '李四',
    CaseType: '民事案件',
    CaseStatus: '已结案',
    RoleList: [{ Desc: '原告' }, { Desc: '被告' }, { Desc: '第三人' }],
    Prosecutor: [
      { KeyNo: 'key1', Name: '原告公司1' },
      { KeyNo: 'key2', Name: '原告公司2' },
    ],
    Appellee: [{ KeyNo: 'key3', Name: '被告公司1' }],
    ThirdPartyList: [{ KeyNo: 'key4', Name: '第三人公司1' }],
    OtherPartyList: [{ KeyNo: 'key5', Name: '其他当事人1' }],
  };

  test('应该正确渲染基本表格结构', () => {
    const wrapper = mount(LianDimension, {
      propsData: {
        viewData: defaultViewData,
      },
    });

    expect(wrapper.html()).toMatchSnapshot();
  });

  test('应该正确处理空的viewData', () => {
    const wrapper = mount(LianDimension, {
      propsData: {
        viewData: {},
      },
    });

    expect(wrapper.html()).toMatchSnapshot();
  });
});
