import { snapdom as snapDom, preCache } from '@zumer/snapdom';

export function downloadDataURL(dataURL: string, fileName: string) {
  const link = document.createElement('a');
  if (typeof link.download !== 'string') {
    window.open(dataURL);
  } else {
    link.href = dataURL;
    link.download = fileName;
    link.click();
  }
}

/**
 * 预缓存资源以提升截图性能
 * 在页面加载完成后调用此函数可以预加载图片和字体资源
 */
export async function preCacheResources(element?: HTMLElement) {
  try {
    await preCache(element || document.body, {
      embedFonts: false, // 为了性能，只预缓存图片
    });
  } catch (error) {
    console.warn('Failed to preCache resources:', error);
  }
}

/**
 * 处理 canvas 元素，将其转换为图片以供 snapDOM 正确捕获
 */
function processCanvasElements(element: HTMLElement): Promise<() => void> {
  return new Promise((resolve) => {
    const canvases = element.querySelectorAll('canvas');
    const restoreFunctions: Array<() => void> = [];

    if (canvases.length === 0) {
      resolve(() => {}); // 如果没有 canvas，直接返回空的恢复函数
      return;
    }

    let processedCount = 0;
    const totalCanvases = canvases.length;

    canvases.forEach((canvas) => {
      try {
        // 检查 canvas 是否已被污染
        const isCanvasTainted = () => {
          try {
            canvas.toDataURL();
            return false;
          } catch (e) {
            return true;
          }
        };

        if (isCanvasTainted()) {
          console.warn('Canvas is tainted, skipping conversion');
          processedCount++;
          if (processedCount === totalCanvases) {
            resolve(() => restoreFunctions.forEach((fn) => fn()));
          }
          return;
        }

        // 将 canvas 转换为图片
        const dataURL = canvas.toDataURL('image/png');
        const img = new Image();

        img.onload = () => {
          // 创建包装器来替换 canvas
          const wrapper = document.createElement('div');
          wrapper.style.cssText = canvas.style.cssText;
          wrapper.style.display = 'inline-block';
          wrapper.style.width = canvas.style.width || `${canvas.width}px`;
          wrapper.style.height = canvas.style.height || `${canvas.height}px`;

          // 设置图片样式
          img.style.width = '100%';
          img.style.height = '100%';
          img.style.objectFit = 'contain';

          wrapper.appendChild(img);

          // 替换 canvas
          const parent = canvas.parentNode;
          if (parent) {
            parent.insertBefore(wrapper, canvas);
            parent.removeChild(canvas);

            // 记录恢复函数
            restoreFunctions.push(() => {
              if (wrapper.parentNode) {
                wrapper.parentNode.insertBefore(canvas, wrapper);
                wrapper.parentNode.removeChild(wrapper);
              }
            });
          }

          processedCount++;
          if (processedCount === totalCanvases) {
            resolve(() => restoreFunctions.forEach((fn) => fn()));
          }
        };

        img.onerror = () => {
          console.warn('Failed to convert canvas to image');
          processedCount++;
          if (processedCount === totalCanvases) {
            resolve(() => restoreFunctions.forEach((fn) => fn()));
          }
        };

        img.src = dataURL;
      } catch (error) {
        console.warn('Error processing canvas:', error);
        processedCount++;
        if (processedCount === totalCanvases) {
          resolve(() => restoreFunctions.forEach((fn) => fn()));
        }
      }
    });
  });
}

export async function createScreenshot(el: HTMLElement, format = 'image/png') {
  if (!el) {
    throw new Error('目标元素不存在');
  }

  // 预处理 canvas 元素
  let restoreCanvas: () => void = () => {};
  try {
    restoreCanvas = await processCanvasElements(el);
  } catch (error) {
    console.warn('Failed to process canvas elements:', error);
  }

  // snapDOM 支持的配置选项
  const snapDomOptions = {
    scale: window.devicePixelRatio > 2 ? window.devicePixelRatio : 2,
    backgroundColor: 'transparent', // 默认为透明背景
    embedFonts: false, // 默认不嵌入字体，性能更好
    compress: true, // 压缩输出
    fast: false, // 关闭快速模式以确保canvas正确渲染
    // snapDOM的crossOrigin配置，用于处理跨域图片
    crossOrigin: (url: string) => {
      // 同源图片使用凭证
      if (url.startsWith(window.location.origin)) {
        return 'use-credentials';
      }
      // 跨域图片使用匿名模式
      return 'anonymous';
    },
  };

  // 根据格式确定输出类型
  const formatType = format.replace('image/', '');

  try {
    // 使用snapDOM捕获元素
    let dataURL: string;

    if (formatType === 'png') {
      const img = await snapDom.toPng(el, snapDomOptions);
      dataURL = img.src;
    } else if (formatType === 'jpeg' || formatType === 'jpg') {
      const img = await snapDom.toJpg(el, {
        ...snapDomOptions,
        quality: 1, // 最高质量
        backgroundColor: '#ffffff', // JPG需要背景色
      });
      dataURL = img.src;
    } else if (formatType === 'webp') {
      const img = await snapDom.toWebp(el, {
        ...snapDomOptions,
        quality: 1, // 最高质量
        backgroundColor: '#ffffff', // WebP需要背景色
      });
      dataURL = img.src;
    } else {
      // 默认使用PNG
      const img = await snapDom.toPng(el, snapDomOptions);
      dataURL = img.src;
    }

    return dataURL.replace(format, 'image/octet-stream');
  } catch (error) {
    console.error('snapDOM capture failed:', error);
    // 提供更友好的错误信息
    if (error instanceof Error) {
      throw new Error(`截图失败: ${error.message}`);
    }
    throw new Error('截图失败，请稍后重试');
  } finally {
    // 恢复原始canvas元素
    try {
      restoreCanvas();
    } catch (error) {
      console.warn('Failed to restore canvas elements:', error);
    }
  }
}

export async function downloadScreenshot(el: HTMLElement, filename = `${Date.now()}.png`, format = 'image/png') {
  try {
    const dataURL = await createScreenshot(el, format);
    downloadDataURL(dataURL, filename);
  } catch (error) {
    console.error('下载截图失败:', error);
    throw error;
  }
}
