// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`TaskPage > should mount correctly 1`] = `
<hero-layout-stub loading="true">
  <div class="root">
    <div class="body" style="padding-top: 0px;">
      <div class="container">
        <div class="main">
          <div class="root offset q-filter__root">
            <div class="group block q-filter-group--filters q-filter-group lastGroup">
              <div class="label q-filter-label" style="width: auto; padding-top: 0px;">筛选条件</div>
              <div class="wrap wrapGroups q-filter-wrap--filters">
                <div class="flex items-center flex-wrap" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
                  <q-select multiple="true" options="[object Object],[object Object],[object Object],[object Object]" class="select"></q-select>
                  <q-select multiple="true" options="[object Object],[object Object],[object Object],[object Object]" class="select"></q-select>
                  <q-select multiple="true" options="" class="select"></q-select>
                  <q-select options="[object Object],[object Object],[object Object],[object Object],[object Object],[object Object]" custom="[object Object]" class="select"></q-select>
                  <div style="display: inline-block;">
                    <div class="container tiny-search__container">
                      <div class="default tiny-search__default">
                        <div class="input">
                          <q-icon-stub type="icon-sousuo"></q-icon-stub><span>搜索</span>
                        </div>
                      </div>
                      <div class="search" style="width: 258px; display: none;">
                        <div class="inputWrapper"><span class="ant-input-search ant-input-search-enter-button ant-input-group-wrapper input tiny-search__input"><span class="ant-input-wrapper ant-input-group"><span class="ant-input-search ant-input-search-enter-button ant-input-affix-wrapper"><input placeholder="请输入任务名称" type="text" class="ant-input"><span class="ant-input-suffix"></span></span><span class="ant-input-group-addon"><button type="button" class="ant-btn ant-btn-primary tiny-search__search ant-input-search-button"><q-icon-stub type="icon-sousuo"></q-icon-stub></button></span></span></span></div>
                        <div class="clear" style="display: none;"><a class="tiny-search__cancel">取消</a></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex items-center">
                  <div style="display: inline-block;">
                    <div class="aside" style="display: none;"><button type="button" class="ant-btn ant-btn-link">
                        <q-icon-stub type="icon-chexiaozhongzhi"></q-icon-stub><span>重置筛选</span>
                      </button></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="root" emptysize="100px" showindex="true">
    <div class="header">
      <div class="wrapper border">
        <div class="title">
          <div class="container"><span>共找到<i aria-label="icon: sync" class="anticon anticon-sync"><svg viewBox="64 64 896 896" data-icon="sync" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" class="anticon-spin"><path d="M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 0 1 755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 0 0 3 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 0 0 8 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 0 1 512.1 856a342.24 342.24 0 0 1-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 0 0-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 0 0-8-8.2z"></path></svg></i><em style="display: none;">0</em>条相关结果</span></div>
        </div>
        <div class="extra">
          <div></div>
        </div>
      </div>
    </div>
    <div class="body" style="padding: 15px;">
      <div class="container">
        <div class="ant-spin-nested-loading">
          <div>
            <div class="ant-spin ant-spin-spinning"><span class="ant-spin-dot ant-spin-dot-spin"><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i></span></div>
          </div>
          <div class="ant-spin-container ant-spin-blur">
            <div class="ant-table-wrapper table scrollable" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
              <div class="ant-spin-nested-loading">
                <div class="ant-spin-container">
                  <div class="ant-table ant-table-scroll-position-left ant-table-default ant-table-bordered ant-table-empty">
                    <div class="ant-table-content">
                      <!---->
                      <div class="ant-table-body">
                        <table class="">
                          <colgroup>
                            <col style="width: 58px; min-width: 58px;">
                          </colgroup>
                          <thead class="ant-table-thead">
                            <tr>
                              <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word ant-table-row-cell-last" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                      </div></span></th>
                      </tr>
                      </thead>
                      <tbody class="ant-table-tbody"></tbody>
                      </table>
                    </div>
                    <div class="ant-table-placeholder">
                      <div class="empty"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
</hero-layout-stub>
`;
