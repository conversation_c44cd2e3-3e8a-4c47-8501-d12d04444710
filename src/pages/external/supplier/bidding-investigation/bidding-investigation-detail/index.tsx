import { computed, defineComponent, onMounted, onUnmounted, ref } from 'vue';
import { Button, Spin, Tooltip, message } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { debounce } from 'lodash';

import { scrollTo, useBiddingDetail } from '@/shared/composables/use-bidding-detail';
import TenderResultBlock from '@/pages/supplier/bidding-investigation-detail/components/tender-result-block';
import TenderTitleBlock from '@/pages/supplier/bidding-investigation-detail/components/tender-title-block';
import TenderDimensionBlock from '@/pages/supplier/bidding-investigation-detail/components/tender-dimension-block';
import { useDOMScreenshot } from '@/hooks/use-dom-screenshot';
import RiskAction from '@/shared/components/risk-action';
import { useAbility } from '@/libs/plugins/user-ability';
import { biddings as biddingsService } from '@/shared/services';
import { useRoomSocket } from '@/hooks/use-room-socket/use-room-socket';
import { createTrackEvent } from '@/config/tracking-events';
import { GenerateReportStatus, useGenerateReportFile } from '@/shared/composables/use-generate-report-file';
import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';
import { useBiddingSettingStore } from '@/hooks/use-bidding-setting-store';
import GenerateReportAction from '@/components/generate-report-action';
import { BatchBusinessTypeEnum } from '@/shared/constants/batch-business-type.constant';

const ExternalBiddingInvestigationDetailPage = defineComponent({
  name: 'ExternalBiddingInvestigationDetailPage',
  setup() {
    const isOvertime = ref(false);
    const route = useRoute();
    const router = useRouter();

    // 依赖 TenderSettingStore 来组成 dimensionHitsDetails;
    const { getSetting: getBiddingSettingInfo } = useBiddingSettingStore();

    // 获取详情数据
    const { search, refreshSnapshot: refreshBidSnapshot, updateTenderTitle, result, dimensionHitsDetails, loading } = useBiddingDetail();
    const isCurrent = computed(() => Number(result.value?.id) === Number(route.query.id));
    const isBidding = computed(() => isCurrent.value && result.value?.status === 0);
    const isFailed = computed(() => isCurrent.value && result.value?.status === 2);

    const handleFetchData = async () => {
      try {
        await getBiddingSettingInfo();
        const res = await search();
        if (router.currentRoute.name === 'bidding-investigation-detail' && !route.query.id) {
          router.replace({
            name: String(route.name),
            query: {
              id: res.value.id,
            },
          });
        }
      } catch (error) {
        if (error.message.includes('403')) {
          router.replace({
            name: 'bidding-investigation-failed',
            params: route.params,
            query: { ...route.query, auth: 'failed' },
          });
        }
      }
    };

    let timeoutTimerId: number | undefined = undefined;

    onMounted(() => {
      handleFetchData();
      timeoutTimerId = setTimeout(() => {
        isOvertime.value = true;
      }, 8000);
    });

    onUnmounted(() => {
      clearTimeout(timeoutTimerId);
    });

    /**
     * 接收到排查成功的消息，刷新页面
     * FIXME: 使用 debounce 避免 websocket 重复推送的BUG
     */
    const socketUpdater = debounce((data: Record<string, any>) => {
      // NOTE: data.id 为字符串, route.query.id 为数字
      const isMatched = data.id === String(route.query.id);
      const statusChanged = data.status !== String(result.value?.status);
      const isDone = data.status === '1';
      if (isMatched && statusChanged) {
        search();
        if (isDone) {
          clearTimeout(timeoutTimerId); // 清除计时器，避免无效超时
        }
      }
    }, 500);

    useRoomSocket('/rover/socket', {
      eventType: 'TenderDiligenceChanged',
      filter: (messageData) => messageData.roomType === 'TenderDiligence',
      update: socketUpdater,
    });

    const [pageRef, downloadScreenshot] = useDOMScreenshot();

    const ability = useAbility();

    /** 轮询报告生成状态 */
    const [genReportState, genReportPolling, resetGenReportState] = useGenerateReportFile(BatchBusinessTypeEnum.Tender_Report_Export);

    const refreshSnapshot = async () => {
      await refreshBidSnapshot(() => {
        resetGenReportState();
      });
    };

    /** 生成报告 */
    const handleGenerateReport = async () => {
      if (!(await ability.check('stock', ['DiligenceReportQuantity']))) {
        return;
      }
      if (!result.value.id) {
        message.warning('数据异常，请刷新重试');
        return;
      }
      try {
        genReportState.status = GenerateReportStatus.Pending;
        const { batchId, businessType } = await biddingsService.pollingGenerateReport(result.value?.id);
        message.success('您的报告正在生成中，成功后我们将第一时间提醒您');
        // 嵌入页面支持监听 batchJob
        window.parent.postMessage(
          {
            type: 'createBatchJob',
            data: {
              batchId,
              businessType,
            },
          },
          '*'
        );
        genReportPolling(batchId);
      } catch (error) {
        console.error(error);
        genReportState.status = GenerateReportStatus.Idle;
        const errorMessage = error?.response?.data?.error ?? '报告生成失败，请稍后再试';
        message.error(errorMessage);
      }
    };

    const handleRetry = async () => {
      try {
        if (!result.value.id) {
          throw new Error('id not found');
        }
        await biddingsService.retry(result.value.id);
        message.success('重试成功');
        search();
      } catch (err) {
        console.error(err);
      }
    };

    // 隐藏人工复核操作
    const hideReviewAction = computed(() => route.query.hideReviewAction === 'Y');
    // 隐藏排查明细的操作列
    const hideEligibleAction = computed(() => route.query.hideEligibleAction === 'Y');

    return {
      dimensionHitsDetails,
      loading,
      pageRef,
      downloadScreenshot,
      handleGenerateReport,
      updateTenderTitle,
      genReportState,
      result,
      search,
      refreshSnapshot,
      handleRetry,
      isOvertime,
      isBidding,
      isCurrent,
      isFailed,

      hideReviewAction,
      hideEligibleAction,
    };
  },
  render() {
    const renderContent = () => {
      const loadingContainerStyle = {
        backgroundColor: '#fff',
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: '4px',
      };

      if (this.isFailed) {
        return (
          <section style={loadingContainerStyle}>
            <QRichTableEmpty size={'100px'}>
              <div style={{ marginBottom: '30px' }}>排查失败</div>
              {this.isCurrent && (
                <Button type="primary" v-debounceclick={this.handleRetry}>
                  重试
                </Button>
              )}
            </QRichTableEmpty>
          </section>
        );
      }

      if (this.isBidding) {
        return (
          <section style={loadingContainerStyle}>
            <Spin spinning>
              <div style={{ marginBottom: '100px' }}>
                {this.isOvertime
                  ? '您本次排查的企业关联路径较为复杂，系统努力运算中，请您耐心等待，或稍后在排查记录中查看排查结果'
                  : '正在排查中，请稍后…'}
              </div>
            </Spin>
          </section>
        );
      }

      if (this.loading) {
        return (
          <section style={loadingContainerStyle}>
            <Spin spinning />
          </section>
        );
      }

      return (
        <section ref="pageRef">
          {/* 基础信息 */}
          <TenderTitleBlock
            hideReviewAction={this.hideReviewAction}
            dataSource={this.result}
            onSearch={this.search}
            onUpdateTitle={this.updateTenderTitle}
            saveFn={biddingsService.addRemark}
          >
            <template slot="extra">
              <Tooltip title="点击将刷新页面重新排查">
                <RiskAction
                  icon="icon-icon_sqq"
                  v-debounceclick={() => {
                    this.refreshSnapshot();
                    this.$track(createTrackEvent(6933, '招标排查详情', '重新排查'));
                  }}
                  theme="slight"
                >
                  重新排查
                </RiskAction>
              </Tooltip>

              <RiskAction data-capture="exclude" onClick={this.downloadScreenshot} icon="icon-icon_gongshangkuaizhao">
                生成快照
              </RiskAction>

              <GenerateReportAction permission={[2115]} genReportState={this.genReportState} onGenerateReport={this.handleGenerateReport} />
            </template>
          </TenderTitleBlock>
          {/* 招标排查排查结果: 风险简析,排查企业明细 */}
          <TenderResultBlock
            hideEligibleAction={this.hideEligibleAction}
            dataSource={this.result}
            dimensionHitsDetails={this.dimensionHitsDetails}
            onSearch={this.search}
            style={{ marginTop: '15px' }}
          ></TenderResultBlock>

          {/* 招标排查维度详情页 */}
          <TenderDimensionBlock
            dataSource={this.result}
            dimensionHitsDetails={this.dimensionHitsDetails}
            onScrollTo={scrollTo}
            style={{ marginTop: '15px' }}
            navOffsetTop="0"
          />
        </section>
      );
    };

    return (
      <div
        class="workbench-layout-main"
        style={{
          overflow: 'auto',
          height: '100vh',
        }}
      >
        {renderContent()}
      </div>
    );
  },
});

export default ExternalBiddingInvestigationDetailPage;
