import { mount } from '@vue/test-utils';

import InterestInvestigationBatchBatchPage from '..';
import { biddings } from '@/shared/services';

vi.mock('@/shared/services');

describe('InterestInvestigationBatchBatchPage', () => {
  beforeEach(() => {
    vi.mocked(biddings).getBatchList.mockResolvedValue({
      data: [],
    });
  });
  afterEach(() => {
    vi.resetAllMocks();
  });
  it('测试组件是否正确渲染', async () => {
    const wrapper = mount(InterestInvestigationBatchBatchPage, {});
    expect(wrapper.html()).toMatchSnapshot();
  });
});
