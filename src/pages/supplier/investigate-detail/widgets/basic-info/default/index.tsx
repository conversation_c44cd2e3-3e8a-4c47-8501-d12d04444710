import { Popover, Tooltip, message } from 'ant-design-vue';
import { useResizeObserver } from '@vueuse/core';
import { computed, defineComponent, nextTick, ref } from 'vue';
import { get, isObject, isString } from 'lodash';

import QCopy from '@/components/global/q-copy';
import { dateFormat } from '@/utils/format';
import { numberToHuman } from '@/utils/number-formatter';
import { copyToClipboard, translateRegistCapiLabel } from '@/utils';
import { createTrackEvent } from '@/config/tracking-events';
import { SCALE_SIZE_ICON_MAP } from '@/shared/constants/company-scale-size-icon.constant';
import { getActingList, getOperTypeLabelMapper, getSameList } from '@/utils/firm';
import QIcon from '@/components/global/q-icon';
import QCompanyStatus from '@/components/global/q-company-status';
import QEntityLink from '@/components/global/q-entity-link';
import QPhoneStatus from '@/components/global/q-phone-status';
import NationTag from '@/shared/components/nation-tag';

import { BASICCONFIG, basicInfoDetailSettings, isEmptyValue, isRange } from './config/basic-info.config';
import QCopyWrapper from './widgets/q-copy-wrapper';
import styles from './basic-info-default.module.less';
import StaffPopInfo from './widgets/staff-pop-info';

const ToolTipMap = {
  assetsTotal: {
    0: '来自公开披露的企业资产数据，包含且不仅限于企业工商年报披露的资产数据、企业证券公告披露的合并报表和母公司报表的资产数据（其中在境外上市的境内运营主体数据来自其对应上市公司披露的资产数据）以及其他公开数据来源。',
    1: '基于大数据模型，结合不同行业企业的经营情况数据，建立算法模型分析的企业资产规模可能分布区间。仅用以辅助体现主体的综合信用情况，并不代表企查查任何明示、暗示之观点或保证。',
  },
  operIncTotal: {
    0: '来自公开披露的企业营业收入，其中上市企业根据上市公告披露的合并报表的营业收入，非上市企业根据最新工商年报披露的营业收入。',
    1: '基于大数据模型，结合不同行业企业的经营情况数据，建立算法模型分析的企业营业收入可能分布区间。仅用以辅助体现主体的综合信用情况，并不代表企查查任何明示、暗示之观点或保证。',
  },
  netProfit: {
    0: '来自公开披露的企业净利润数据，包含且不仅限于企业工商年报披露的净利润、企业证券公告披露的合并报表和母公司报表的净利润（其中在境外上市的境内运营主体数据来自其对应上市公司披露的净利润）以及其他公开数据来源。',
    1: '基于大数据模型，结合不同行业企业的经营情况数据，建立算法模型分析的企业净利润可能分布区间。仅用以辅助体现主体的综合信用情况，并不代表企查查任何明示、暗示之观点或保证。',
  },
};

const BasicInfoDefault = defineComponent({
  name: 'BasicInfoDefault',
  props: {
    company: {
      type: Object,
      required: true,
    },
    tags: {
      type: Array,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const IconType = ref('icon-a-xianduanxia');
    const snapshot = () => {
      emit('snapshot');
    };
    const addressContentRef = ref<Element>();
    const reportAddrRef = ref<Element>();
    const companyName = ref('');
    const needWrap = ref(false); // 是否需要换行
    const key = ref(0);
    companyName.value = props.company?.Name || '';

    // 如果公司名称过长需要换行显示，第二行开头不能是）
    const bodyWatch = () => {
      companyName.value = props.company?.Name;
      key.value++;
      nextTick(() => {
        const statusDiv = document.getElementsByClassName('qcStatus')[0]?.getClientRects()[0];
        if (statusDiv?.top > 215 && statusDiv?.left >= 205 && statusDiv?.left <= 215) {
          needWrap.value = true;
          const nameLength = props.company?.Name.length;
          const name = props.company?.Name.split('');
          const charCount = name[nameLength - 1] === ')' || name[nameLength - 1] === '）' ? 2 : 1;
          name.splice(nameLength - charCount, 0, '\n');
          companyName.value = name.join('');
        }
      });
    };

    useResizeObserver(document.body, () => {
      setTimeout(() => {
        bodyWatch();
      }, 0);
    });

    const changeIconType = (val) => {
      IconType.value = val ? 'icon-a-xianduanshang' : 'icon-a-xianduanxia';
    };

    const contactInfo = computed(() => {
      return props.company?.ContactInfo || {};
    });

    const samePhoneList = computed(() => {
      return getSameList(props.company);
    });

    return {
      IconType,
      changeIconType,

      key,
      companyName,
      needWrap,
      snapshot,
      addressContentRef,
      reportAddrRef,
      contactInfo,
      samePhoneList,
    };
  },

  methods: {
    genPhoneField() {
      const that = this as any;
      const { company, contactInfo, samePhoneList } = that;
      const contentNode: any = [];
      const phone = contactInfo?.PhoneNumber;
      if (!phone) {
        return '-';
      }
      contentNode.push(
        <span>
          <QPhoneStatus phone={phone} vtList={company.VTList} /> {phone}
        </span>
      );
      if (company?.HisTelList && company?.HisTelList?.length) {
        contentNode.push(
          <a
            class={styles.fieldPadding}
            onClick={() => {
              const currentPhone = {
                Tel: phone,
                SourceFrom: contactInfo.TelSource,
              };
              const hisTelList = [currentPhone, ...company.HisTelList];
              (this as any).onShowContactMore('tel', hisTelList);
            }}
          >
            更多 {company?.HisTelList?.length}
          </a>
        );
      }
      if (samePhoneList.phone) {
        contentNode.push(
          <a
            class={styles.fieldPadding}
            onClick={() => {
              (this as any).onPhoneSameListClick(`疑似同电话企业 ${samePhoneList.phone.count}`, samePhoneList.phone);
              this.handleTracker('同电话企业');
            }}
          >
            同电话企业 {samePhoneList.phone.count}
          </a>
        );
      }
      return contentNode;
    },
    onShowContactMore(type, list) {
      const that = this as any;
      if (type === 'email') {
        this.handleTracker('更多邮箱');
        that.$modal.showDimension('companyContact', { title: `更多邮箱 ${list.length}`, size: 'medium' }, { type, list });
      } else if (type === 'tel') {
        this.handleTracker('更多电话');
        that.$modal.showDimension(
          'companyContact',
          {
            title: `电话 ${list.length}`,
            size: 'medium',
          },
          {
            type,
            list: list.map((v) => {
              return { t: v.Tel, s: v.SourceFrom };
            }),
            info: that.company,
          }
        );
      }
    },
    onPhoneSameListClick(title, field) {
      const that = this as any;
      const actingList = getActingList(that.company);
      // console.log(actingList, '0000', title, field, that.company)
      if (field.value && actingList[field.value]) {
        // eslint-disable-next-line no-param-reassign
        field.isActing = true;
      }
      that.$modal.showDimension('companySamePhone', {
        title,
        data: {
          ...field,
        },
      });
    },

    handleTracker(btnName) {
      this.$track(createTrackEvent(6987, '准入排查详情页', btnName, '公司概况'));
    },
  },
  render() {
    const { company } = this;
    let industryInfo = '';
    let copyIndustryInfo = '';
    if (company?.QccIndustry?.Dn) {
      copyIndustryInfo = `${company?.QccIndustry?.An} > ${company?.QccIndustry?.Bn} > ${company?.QccIndustry?.Cn} > ${company?.QccIndustry?.Dn}`;
      industryInfo = `${company?.QccIndustry?.An} > ${company?.QccIndustry?.Bn} > ${company?.QccIndustry?.Cn} > `;
    } else if (company?.QccIndustry?.Cn) {
      copyIndustryInfo = `${company?.QccIndustry?.An} > ${company?.QccIndustry?.Bn} > ${company?.QccIndustry?.Cn}`;
      industryInfo = `${company?.QccIndustry?.An} > ${company?.QccIndustry?.Bn} > `;
    } else {
      copyIndustryInfo = `${company?.QccIndustry?.An} > ${company?.QccIndustry?.Bn}`;
      industryInfo = `${company?.QccIndustry?.An} > `;
    }

    const renderTitle = (name, explain) => {
      return [
        name,
        <Tooltip placement="bottom">
          <div slot="title" domPropsInnerHTML={explain}></div>
          <QIcon type="icon-zhushi" style={{ color: '#D8D8D8' }} />
        </Tooltip>,
      ];
    };

    const labelRenderMap = {
      operTypeLabel: (val) => getOperTypeLabelMapper(val),

      registCapiLabel: () => {
        const isOrg = company.KeyNo?.startsWith('s');
        const label = translateRegistCapiLabel(isOrg ? 'org' : company.standardCode);
        return renderTitle(label, '一般指企业在登记机关登记注册的资本额，也叫法定资本，为股东认缴的出资总额或者发起人认购的股本总额。');
      },

      // 资产规模
      assetsTotalLabel: () => renderTitle('资产规模', ToolTipMap.assetsTotal[Number(isRange(company.assetsTotal))]),

      // 住所 注册地址
      addressLabel: () => {
        return company.Type === 1 ? '住所' : '注册地址';
      },

      operIncTotalLabel: () =>
        renderTitle('营业收入', ToolTipMap.operIncTotal[Number(isRange(company?.operIncTotal || company?.CompanyRevenue?.Revenue))]),

      netProfitLabel: () => renderTitle('净利润', ToolTipMap.operIncTotal[Number(isRange(company?.netProfit))]),

      ScaleLabel: () =>
        renderTitle(
          '企业规模',
          `基于大数据模型，结合不同行业企业的经营数据（其中上市/发债企业以合并报表口径）计算形成的<b>L(大型)</b>、<b>M(中型)</b>、<b>S(小型)</b>和<b>XS(微型)</b>四类企业规模分类体系（部分缺乏判定指标或新成立的企业不纳入计算范畴）。`
        ),

      StaffsLabel: () =>
        renderTitle(
          '员工人数',
          '来自公开披露的企业员工人数，包含且不仅限于企业证券公告披露的合并报表和母公司报表的员工总数以及其也公开数据来源。'
        ),
      RecCapLabel: () => {
        const { Year, Source } = company.RealCapiSource ?? {};
        const sourceText1 = Year && +Source === 4 ? `实缴资本信息来源：${Year}年报，数据仅供参考。` : '';
        const sourceText2 = Year && +Source === 7 ? `实缴资本信息来源：${Year}年证券公告等公开数据源，数据仅供参考。` : '';
        const sourceText3 = !Year && +Source === 7 ? `实缴资本信息来源：证券公告等公开数据源，数据仅供参考。` : '';
        const sourceText = sourceText1 || sourceText2 || sourceText3;
        return renderTitle(
          '实缴资本',
          `企业实际收到的投资人投入的资本。除部分行业实行注册资本实缴制外，其他企业全部实施认缴登记制，即一般企业无需强制登记实缴资本。
          <div style="width: 100%; height: 1px; background-color: #EBEBEB; margin: 5px 0; display: ${sourceText ? 'block' : 'none'};"></div>
          ${sourceText}
          `
        );
      },
    };

    const contentRenderMap = {
      creditCode: (val) => <QCopyWrapper text={val} />,

      // 法定代表人label
      operTypeContent: (val) => {
        const { Oper, MultipleOper } = company;
        const renderOps = MultipleOper?.OperList?.length === 1 ? [Oper] : MultipleOper?.OperList;
        return (
          <Tooltip mouseEnterDelay={Oper.Name?.length > 20 ? 0 : 9999} overlayClassName={styles.oper}>
            <div slot="title">
              <QEntityLink coyArr={renderOps}></QEntityLink>
            </div>
            <QEntityLink coyObj={Oper} style={{ maxWidth: '200px' }}></QEntityLink>
          </Tooltip>
        );
      },

      // 资产规模
      assetsTotalContent: (val) => [
        val,
        company?.assetsTotalSource ? <i class={styles.basiclabel}>{`(${company?.assetsTotalSource})`}</i> : '',
      ],

      StartDate: (val) => {
        const time = dateFormat(val);
        return <QCopyWrapper text={time} />;
      },

      // 住所
      addressContent: (val) => {
        return <QCopyWrapper text={val} icon={'icon-a-zuobiaoxian'} />;
      },

      // 注册地址
      LatestAnnualReportAddrInfo: (data) => {
        if (!data?.length) {
          return '-';
        }
        return data.map((reportAddr) => {
          return (
            <QCopyWrapper
              text={reportAddr.Address}
              icon={'icon-a-zuobiaoxian'}
              copy-value={`${reportAddr.Address} ${reportAddr.Year ? `(${reportAddr.Year}年年报)` : ''}`}
            >
              <span
                style={{
                  display: 'inline-block',
                  marginRight: '2px',
                }}
                class={styles.basiclabel}
              >{`${reportAddr.Year ? `(${reportAddr.Year}年年报)` : ''}`}</span>
            </QCopyWrapper>
          );
        });
      },

      // 实缴资本 注册资本
      humanNumber: (value) => numberToHuman(value),

      // 营业收入
      operIncTotalContent: (val) => {
        return [
          !isEmptyValue(val) ? numberToHuman(val) : numberToHuman(company?.CompanyRevenue?.Revenue),
          company?.operIncTotalSource ? <i class={styles.basiclabel}>{`(${company.operIncTotalSource})`}</i> : '',
        ];
      },

      // 电话
      PhoneNumber: () => this.genPhoneField(),

      // 所属行业
      QccIndustry: () => (
        <Popover
          placement="bottomRight"
          overlayClassName={styles.menuPopover}
          onVisibleChange={(val) => {
            if (val) {
              this.handleTracker('所属行业');
            }
            this.changeIconType(val);
          }}
        >
          <template slot="content">
            <div class={styles.industryTextWrapper}>
              <span class={styles.industryText}>
                <span style={{ color: '#BBBBBB' }}>{industryInfo}</span>
                <span>{company?.QccIndustry?.Dn || company?.QccIndustry?.Cn || company?.QccIndustry?.Bn || company?.QccIndustry?.An}</span>
                <span
                  class={styles.copyText}
                  onClick={() => {
                    try {
                      copyToClipboard(copyIndustryInfo);
                      message.success('复制成功');
                    } catch {
                      message.error('复制失败');
                    }
                  }}
                >
                  <QIcon class={[styles.icon]} type="icon-gongyingshangchouqu" />
                  <span style={{ marginLeft: '4px' }}>复制</span>
                </span>
              </span>
            </div>
          </template>
          <span class={styles.industryInfo}>
            {company?.QccIndustry?.Dn || company?.QccIndustry?.Cn || company?.QccIndustry?.Bn || company?.QccIndustry?.An}
          </span>
          <QIcon class={styles.icon} type={this.IconType} />
        </Popover>
      ),

      // 企业规模
      ScaleContent: () => [<QIcon type={SCALE_SIZE_ICON_MAP[company?.Scale]} class={styles.scaleIcon} />, company?.Scale || '-'],

      // 员工人数
      StaffsContent: () => {
        const { PopUpList, Top } = company?.StaffHisCntInfo || {};
        if (!PopUpList || !Top) {
          return '-';
        }
        const { Data, Year } = Top;
        return [
          numberToHuman(Data.replace('人民币', '')),
          '人',
          Year ? <i class={styles.basiclabel}>{`(${Year}年年报)`}</i> : '',
          company?.StaffHisCntPopUpInfo ? <StaffPopInfo companyInfo={company} /> : '',
        ];
      },

      // 净利润
      netProfitContent: () => [
        company?.netProfit,
        company?.netProfitSource ? <i class={styles.basiclabel}>{`(${company?.netProfitSource})`}</i> : '',
      ],
    };

    /**
     * 解析 scopeSlots 配置，提取标签渲染器和内容渲染器
     * @param scopeSlots - 插槽配置，可以是字符串或对象
     * @returns 返回解析后的渲染器配置
     */
    const parseScopeSlots = (scopeSlots: any) => {
      let contentRenderKey = '';
      let labelRenderKey = '';

      if (isString(scopeSlots)) {
        contentRenderKey = scopeSlots;
      }

      if (isObject(scopeSlots)) {
        const { labelRender, contentRender } = scopeSlots as Record<'labelRender' | 'contentRender', any>;
        contentRenderKey = contentRender ?? contentRenderKey;
        labelRenderKey = labelRender ?? labelRenderKey;
      }

      return { contentRenderKey, labelRenderKey };
    };

    /**
     * 获取最终的标签文本
     * @param label - 原始标签
     * @param labelRenderKey - 标签渲染器key
     * @returns 返回最终显示的标签文本
     */
    const getFinalLabelText = (label: string | undefined, labelRenderKey: string) => {
      return label || labelRenderMap?.[labelRenderKey]?.(company?.Oper?.OperType);
    };

    /**
     * 获取最终的内容渲染器
     * @param contentRenderKey - 内容渲染器key
     * @returns 返回最终的内容渲染函数
     */
    const getFinalContentRenderer = (contentRenderKey: string) => {
      return contentRenderMap?.[contentRenderKey] || contentRenderKey;
    };

    /**
     * 渲染单个基本信息项
     * @param item - 基本信息配置项
     * @returns 返回渲染的JSX元素或null
     */
    const renderBasicInfoItem = (item: BASICCONFIG) => {
      const { label, key, scopeSlots, hide } = item;

      // 如果配置了隐藏条件且满足隐藏条件，则不渲染
      if (hide && hide(company)) {
        return null;
      }

      // 获取公司数据中对应字段的值
      const contentValue = get(company, key, undefined);

      // 解析 scopeSlots 配置
      const { contentRenderKey, labelRenderKey } = parseScopeSlots(scopeSlots);

      // 获取最终的标签文本和内容渲染器
      const finalLabelText = getFinalLabelText(label, labelRenderKey);
      const finalContentRenderer = getFinalContentRenderer(contentRenderKey);

      // 渲染最终内容
      const finalContent = contentValue && finalContentRenderer ? finalContentRenderer(contentValue) : contentValue || '-';

      return (
        <div class={styles.basicItem}>
          <span class={styles.basiclabel}>{finalLabelText}：</span>
          <span class={styles.basicVal}>{finalContent}</span>
        </div>
      );
    };

    /**
     * 渲染基本信息详情列表
     * @param dataList - 基本信息配置数据列表
     * @returns 返回渲染的基本信息区块
     */
    const renderDetailInfo = (dataList: BASICCONFIG[]) => {
      return <div class={styles.basicPart}>{dataList.map(renderBasicInfoItem)}</div>;
    };
    return (
      <div class={styles.companyInfo}>
        <div class={styles.companyTitle}>
          <div class={styles.title}>
            <QCopy
              key={this.key}
              copyValue={company?.Name}
              copyButtonStyle={{
                whiteSpace: 'nowrap',
              }}
            >
              <span class={styles.titleInner} slot="contain">
                <a
                  href={`/embed/companyDetail?keyNo=${company.KeyNo}&title=${company?.Name || ''}`}
                  class={{ [styles.copyTitle]: true, copyContent: true, [styles.needWrap]: this.needWrap }}
                  target="_blank"
                  domPropsInnerHTML={this.companyName || ''}
                  onClick={() => this.handleTracker('公司主页')}
                ></a>
                <QCompanyStatus class="qcStatus" status={company?.ShortStatus} v-show={company?.ShortStatus} ghost />
                <a
                  class={styles.enterButton}
                  href={`/embed/companyDetail?keyNo=${company.KeyNo}&title=${company?.Name || ''}`}
                  target="_blank"
                  onClick={() => this.handleTracker('进入主页')}
                >
                  进入主页
                </a>
              </span>
            </QCopy>
          </div>
          <div class={styles.extra}>{this.$slots.extra}</div>
        </div>
        <NationTag company={{ ...company, otherTags: this.tags }} />
        <div class={styles.companyBasic}>
          {Object.keys(basicInfoDetailSettings).map((key, index) => {
            if (index !== 2) {
              return [renderDetailInfo(basicInfoDetailSettings[key]), <div class={styles.line} />];
            }
            return renderDetailInfo(basicInfoDetailSettings[key]);
          })}
        </div>
      </div>
    );
  },
});

export default BasicInfoDefault;
