import { useStorage } from '@vueuse/core';
import { cloneDeep, has, omit, pick, sortBy } from 'lodash';
import { unref } from 'vue';

import { useUserStore } from '@/shared/composables/use-user-store';

export const getLocalTableColumns = (key: string): any => {
  const { profile } = useUserStore();
  return useStorage(`${key}-${unref(profile)?.currentOrg}-${unref(profile)?.id}`, []);
};

export const getTableColumns = (params) => {
  const { key, data } = params;
  const newColumn = cloneDeep(data);
  // 表格最新的排序map
  const newIndexMap = newColumn.reduce((indexMap, cur, index) => {
    indexMap[cur.basicTitle ?? cur.title] = index;
    return indexMap;
  }, {});

  const localColumns = getLocalTableColumns(key);
  let newPart: any[] = [];
  // 通过本地的缓存的表格列进行匹配，
  // 如果新的列存在当前列，则保存下来，更新除了显隐之外的其他列；并删除对应的当前列
  if (unref(localColumns)?.length !== 0) {
    newPart = unref(localColumns).reduce((columns: any[], cur: any) => {
      // 自定义的列，使用basicTitle，否则使用title
      const titleKey = cur.basicTitle ? 'basicTitle' : 'title';
      const retainList = [titleKey, 'show'];
      const existIndex = newIndexMap[cur[titleKey]]; // 寻找新的设置中的对应的列
      if (existIndex > -1) {
        columns.push({
          ...pick(cur, retainList), // 保留显隐属性
          ...omit(newColumn[existIndex], retainList),
        });
        newColumn.splice(existIndex, 1, undefined);
      }
      return columns;
    }, []);
    // 得到插入列的位置，如果大于表格总列数，插入最后，不然就在自己的位置
    const getIndex = (item) => {
      const len = newPart.length;
      const titleKey = item.basicTitle ? 'basicTitle' : 'title';
      if (newIndexMap[item[titleKey]] <= len - 1) {
        return newIndexMap[item[titleKey]];
      }
      return -1;
    };
    newColumn
      .filter((column) => column)
      .forEach((element) => {
        const insertIndex = getIndex(element);
        newPart.splice(insertIndex, 0, element);
      });
  } else {
    newPart = newColumn;
  }

  // 左侧固定列顺序不变，且不可拖拽
  localColumns.value = sortBy(newPart, (o) => {
    return o.fixed === 'left' || o.oldFixed === 'left' ? -1 : data.findIndex((c) => c.title === o.title);
  });
  // 遍历，如果没有show，增加对应的属性
  localColumns.value.forEach((item: Record<string, any>) => {
    if (!has(item, 'show')) {
      item.show = true;
    }
  });
  return localColumns;
};
