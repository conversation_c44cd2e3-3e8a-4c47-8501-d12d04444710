/* istanbul ignore file */
import moment from 'moment';
import { getCurrentInstance, VNode } from 'vue';
import { cloneDeep, flatten, get, intersection, isArray, isEmpty, isNil, isNumber, reverse, sortBy, uniq } from 'lodash';
import { But<PERSON> } from 'ant-design-vue';

import QIcon from '@/components/global/q-icon';
import QRoleText from '@/components/global/q-role-text';
import { dateFormat } from '@/utils/format';
import { RISK_LEVEL_CODE_THEME_MAP } from '@/shared/constants/risk-level-code-map.constant';
import QTag from '@/components/global/q-tag';
import QEntityLink from '@/components/global/q-entity-link';
import QSimpleEntityLink from '@/components/global/q-entity-link/simple';
import ClampContent from '@/components/clamp-content';
import { openAtlasChartDialog, openDetailModal, openRelationalChartDialog } from '@/shared/composables/open-dialog-drawer';
import { getTranslatePathItem } from '@/shared/services/diligence.service';
import { getRiskLevelStyle } from '@/config/risk.config';
import FileLogo from '@/components/file-logo';
import { getColor, getStyleByJob } from '@/utils/format/company-format';
import { LAWSUIT_RESULT_CODE_MAP } from '@/shared/constants/lawsuit-result-code-map.constant';
import { punishReasonTypeMap } from '@/apps/setting/pages/investigation-setting/widgets/risk-settings/risk-settings.config';
import { numberToHumanWithUnit } from '@/utils/number-formatter';
import { RelationKeyMap } from '@/config/dimension';
import { parseGraphData } from '@/shared/components/_helpers/data-parser';
import LineDrawer from '@/components/line-drawer';
import openSameInfoDrawer from '@/pages/supplier/bidding-investigation/widgets/relational-table-view/same-info-drawer';
import InlineCollapse from '@/shared/components/inline-collapse';
import AssociationPath from '@/components/association-path';
import { getHighLightDifferent } from '@/shared/constants/string-buffer';
import { getDetailByType } from '@/shared/config/risk-detail.config';
import QGlossaryInfo from '@/components/global/q-glossary-info';
import { hasPermission } from '@/shared/composables/use-permission';

import SuspectVerifyButton from './widgets/suspect-verify-button';
import SuspectInterestConflictVerify from './widgets/suspect-interst-conflict-verify';
import DimensionDetailAction from '../dimension-detail-action';
import styles from './risk-table-next.module.less';

const jobPriorityMap = {
  top0: ['企业主体'],
  top1: ['法定代表人'],
  top2: ['股东', '大股东', '实际控制人', '受益所有人'],
};

const getJobClass = (job) => {
  if (jobPriorityMap.top0.includes(job)) {
    return styles.tagGrey;
  }
  if (jobPriorityMap.top1.includes(job)) {
    return styles.tagBlue;
  }
  if (jobPriorityMap.top2.includes(job)) {
    return styles.tagGold;
  }
  return styles.tagDefault;
};

const getJobArr = (job: string) => {
  if (!job) return [];
  const arr = job.split(',');
  const order = Object.values(jobPriorityMap).flat();
  return intersection(order, arr).concat(arr.filter((v) => !order.includes(v)));
};

const renderTag = (verificationStatus) => {
  if (!verificationStatus) {
    return null;
  }
  let iconType = 'icon-paichuguanlian';
  let text = '排除';
  let color = '#00AD65';
  let bjColor = '#E0F5EC';
  if (verificationStatus === 1) {
    iconType = 'icon-querenguanlian';
    text = '确认';
    color = '#F04040';
    bjColor = '#FFECEC';
  }
  return (
    <div
      style={{
        color,
        background: bjColor,
        borderRadius: '2px',
        padding: '2px 6px',
        gap: '3px',
      }}
      class="flex justify-center items-center"
    >
      <QIcon style={{ fontSize: '16px' }} type={iconType} />
      {text}
    </div>
  );
};

const renderRoleDes = (listItem, keyMap: { name: string | string[]; no: string }, needNo, index) => {
  const { name, no } = keyMap || {};
  const KeyNo = get(listItem, no);
  const Name = (isArray(name) ? name : [name]).map((nameKey) => get(listItem, nameKey)).filter(Boolean)[0];
  return [
    needNo && !isNil(index) ? <span>{index + 1}.</span> : null,
    <QSimpleEntityLink
      value={{
        KeyNo,
        Name,
      }}
      key={index}
    />,
    listItem.JudgeResultDescription ? (
      <span style={{ ...getColor(listItem.JudgeResultDescription) }}>[{listItem.JudgeResultDescription}]</span>
    ) : null,
    listItem.Job ? <span class={[styles.jobPosition, getJobClass(listItem.Job)]}>{listItem.Job}</span> : null,
  ];
};
const renderNameList = (roleObj: Record<string, any[]>, keyMap: { name: string | string[]; no: string } = { name: 'Name', no: 'No' }) => {
  if (isEmpty(roleObj)) {
    return '-';
  }
  return (
    <ClampContent>
      {Object.keys(roleObj).map((key: string) => {
        const list = roleObj[key];
        if (list.length > 0) {
          const needNo = list.length > 1;
          if (list.length === 1) {
            return (
              <div>
                {key}: {renderRoleDes(list[0], keyMap, false, undefined)}
              </div>
            );
          }
          return (
            <div class={list.length === 1 ? 'flex' : ''}>
              <div>{key}：</div>
              {list.map((listItem, index) => (
                <div>{renderRoleDes(listItem, keyMap, needNo, index)}</div>
              ))}
            </div>
          );
        }
        return null;
      })}
    </ClampContent>
  );
};
// 与内部黑名单企业存在投资任职关联，与第三方列表企业存在投资任职关联 图谱
const preDealConnectPath = (item) => {
  const HistoryMap = {
    HISEMPLOY: '历史高管',
    HISLEGAL: '历史法人',
    HISINVEST: '历史股东',
  };
  const underStr = HistoryMap[item.typeDD] || item.job || item.stockPercent || '投资';
  const relatedUnderStr = HistoryMap[item.typeRelated] || item.relatedJob || item.relatedStockPercent;
  let pathArr = [
    {
      KeyNo: item.companyId,
      Name: item.companyName,
      Level: '1',
      isReverse: true,
      underStr,
    },
    {
      KeyNo: item.personId,
      Name: item.personName,
      Level: '2',
      underStr: relatedUnderStr,
    },
    {
      KeyNo: item.relatedCompanyId,
      Name: item.relatedCompanyName,
      Level: '3',
    },
  ];
  if (!item.personId) {
    const path = [
      {
        KeyNo: item.companyId,
        Name: item.companyName,
        Level: '1',
        underStr: item.stockPercent || item.role,
      },
      {
        KeyNo: item.relatedCompanyId,
        Name: item.relatedCompanyName,
        Level: '2',
        underStr: item.stockPercent || item.role,
      },
    ];

    pathArr = item.direction > 0 ? path : reverse(path);
  }

  return pathArr;
};

const preDealControlPath = (scope, meta?, needTitle = true) => {
  let pathData = [];
  const beginData = {
    Name: scope.name,
    KeyNo: scope.keyNo,
  };
  try {
    const oriData = cloneDeep(scope.details?.path) || [];
    pathData = oriData.map((item) => {
      item.unshift(beginData);
      return item.map((data, idx) => {
        return {
          ...data,
          underStr: item[idx + 1] ? item[idx + 1].Percent : data.Percent,
        };
      });
    });
  } catch (error) {
    console.log(error);
  }

  if (pathData?.length) {
    return (
      <ClampContent line={4} clampKey={scope.companyId + meta.tkey}>
        <AssociationPath paths={pathData} showTitle={needTitle}></AssociationPath>
      </ClampContent>
    );
  }
  return '-';
};

export const getPath = (relationObj) => {
  const paths = relationObj.map((item) => {
    const record = getTranslatePathItem(item);
    return preDealConnectPath(record);
  });
  if (paths.length > 0) {
    return (
      <div class="flex">
        <AssociationPath paths={paths} showTitle></AssociationPath>
      </div>
    );
  }
  return '-';
};

// 与内部黑名单企业存在投资任职关联，与第三方列表企业存在投资任职关联 3层穿透 图谱
const preDealRowTemplateV2 = (vm, item, showReason = false) => {
  // 处理roleType,兼容新老数据
  const roleTypes = (item.shortestPath?.length ? flatten(item.shortestPath) : item.relations?.[0] || [])
    .filter((_path) => _path.type === 'edge')
    .reduce((arr, cur) => {
      const groupType = (cur.groups || []).flatMap((group) => group.roleType);
      return [...arr, cur.roleType, ...groupType];
    }, []);
  const relationType = uniq(roleTypes)
    .map((relation: string) => RelationKeyMap[relation.toUpperCase()])
    .join('、');

  return (
    <div class={[styles.boxWrapper, styles.pathBox]}>
      <div class={styles.pathBox} key={item.recordId}>
        {showReason ? <div class={styles.relationType}>{item.reason || '-'}</div> : null}
        <div class={styles.relationType}>{relationType}</div>
        <div class={styles.path}>
          <LineDrawer
            record={item}
            dataKey={'relations'}
            showTag={false}
            onEdgeClick={(data) => {
              vm.$modal.showDimension('ControlRelation', data);
            }}
          />
        </div>
        <div class={styles.tupu}>
          <Button
            type="link"
            onClick={() => {
              const data = parseGraphData([item]);
              openRelationalChartDialog(data);
            }}
          >
            图谱
          </Button>
        </div>
      </div>
    </div>
  );
};
// 与内部黑名单企业存在投资任职关联，与第三方列表企业存在投资任职关联 图谱
const preDealTupu = (relationObj) => {
  // 增加字段，改变图谱的类型
  const record = relationObj.map(getTranslatePathItem);
  record.forEach((recordData: any) => {
    recordData.chartType = 'preset';
  });

  return (
    <Button type="link" onClick={() => openAtlasChartDialog(record)}>
      图谱
    </Button>
  );
};

// 与内部黑名单企业存在投资任职关联，与第三方列表企业存在投资任职关联的模版
export const preDealRowTemplate = (item, showReason = false) => {
  const dataMap = item.relationTypes.reduce((data, cur) => {
    if (data[cur.relationTypeKey]) {
      data[cur.relationTypeKey].data.push({ ...cur });
    } else {
      data[cur.relationTypeKey] = {
        relationType: ['实际控制人', '受益所有人'].includes(cur.relationType) ? `相同${cur.relationType}` : cur.relationType,
        typeDesc: cur.typeDesc?.slice(cur.typeDesc?.includes('相同') ? 2 : 0),
        data: [{ ...cur }],
      };
    }
    return data;
  }, {});
  return (
    <div class={styles.boxWrapper}>
      {Object.values(dataMap).map((relationObj: any) => {
        return (
          <div class={styles.pathBox}>
            {showReason ? <div class={styles.relationType}>{get(relationObj, 'data[0].reason') || '-'}</div> : null}
            <div class={styles.relationType}>{relationObj.relationType}</div>
            <div class={styles.path}>{getPath(relationObj.data)}</div>
            <div class={styles.tupu}>{preDealTupu(relationObj.data)}</div>
          </div>
        );
      })}
    </div>
  );
};

// 第三方、黑名单 疑似关系的数据处理逻辑
const preDealSuspectedRelation = (item, showReason = false, vm) => {
  const relationType = uniq(item.relationTypes).join('、');
  const showVerifyBtn = hasPermission([20094]);
  return (
    <div class={[styles.boxWrapper, styles.pathBox]}>
      <div class={styles.pathBox} key={item.recordId}>
        {showReason ? <div class={styles.relationType}>{item.reason || '-'}</div> : null}
        <div class={styles.relationType}>
          {relationType}
          {renderTag(item.verificationStatus)}
        </div>
        <div class={styles.path}>
          <LineDrawer
            record={item}
            dataKey={'relations'}
            showTag={false}
            onEdgeClick={(data) => {
              if (['Guarantor', 'EquityPledge'].includes(data.roleType)) {
                const startid = data.startid;
                const endid = data.endid;
                const startCompanyObj = item.relations[0].find((obj) => obj.id === startid);
                const endCompanyObj = item.relations[0].find((obj) => obj.id === endid);
                const drawerTitleList = [
                  {
                    companyId: startid,
                    companyName: startCompanyObj.name,
                  },
                  {
                    companyId: endid,
                    companyName: endCompanyObj.name,
                  },
                ];
                openSameInfoDrawer({
                  record: {
                    ...item,
                    drawerTitleList,
                  },

                  type: data.roleType,
                });
              }
            }}
          />
        </div>
        <div class={styles.tupu}>
          <a
            onClick={() => {
              const data = parseGraphData([item]);
              openRelationalChartDialog(data);
            }}
          >
            图谱
          </a>
          <SuspectVerifyButton
            v-show={showVerifyBtn}
            record={{
              ...item,
              ...vm.meta,
              relationType,
              diligenceId: vm.riskInfo.id,
            }}
            onUpdate={() => {
              vm.$emit('update');
            }}
          />
        </div>
      </div>
    </div>
  );
};

/**
 * FIXME: 为每个渲染函数添加测试数据用例
 * @param meta
 * @returns
 */
export const getScopedSlots = (meta) => {
  const instance = getCurrentInstance();
  const vm = instance?.proxy || ({} as any);
  const isNotDynamicDetails = vm?.params?.type !== 'dynamicDetails';

  // WARNING: 下面的h删了会报错
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const h = vm.$createElement;

  // 受益股份样式
  const renderBenifit = (content) => {
    return (
      <div class="flex">
        <QEntityLink coyObj={content} />
        {content.PercentTotal ? <span>，受益股份({content.PercentTotal})</span> : ''}
      </div>
    );
  };

  const pledgCompanyChange = (text, record, key) => {
    // 获取对应字段
    if (!text && !record[key]) {
      return '-';
    }
    let textArr = [];
    //  如果对应key的字段中有值直接用，否则去NameAndKeyNo中进行匹配筛选
    if (record[key]) {
      textArr = record[key];
    } else {
      const textNameList = typeof text === 'string' ? [text] : text;
      textArr = textNameList.reduce((textList, name) => {
        // 从NameAndKeyNo字段中筛选出textNameList对应的KeyNo
        const nameMatch = record.NameAndKeyNo.filter((item) => item.Name === name);
        textList = [...textList, ...nameMatch];
        return textList;
      }, []);
    }

    return <QEntityLink coy-arr={textArr} />;
  };
  const transDate = (text) => {
    if (!text || text < 0 || text === '1970-01-01' || text === '-') {
      return '-';
    }
    if (isNumber(text)) {
      text *= 1000;
    }
    return moment(text).format('YYYY-MM-DD');
  };
  return {
    // 给带单位的数字字符串加千分符
    thousandsNumber: numberToHumanWithUnit,
    date: (text) => {
      return transDate(text);
    },
    name: (item) => {
      const keyNo = item.keyNo || item.KeyNo;
      const name = item.name || item.Name;
      if (!keyNo) {
        return name || '-';
      }
      // 需要判断是否为企业
      const isPerson = keyNo.startsWith('p');
      const url = isPerson
        ? `/embed/beneficaryDetail?personId=${keyNo}&title=${name}`
        : `/embed/companyDetail?keyNo=${keyNo}&title=${name}`;
      return (
        <a href={url} target="_blank">
          {item.name || item.Name}
        </a>
      );
    },
    docNo: (item) => {
      return (
        <a href={`/embed/adminpenaltydetail?id=${item.RiskId || item.riskId}&title=${item.docNo || item.CaseNo}`} target="_blank">
          {item.docNo || item.CaseNo}
        </a>
      );
    },
    qentityArr: (data) => {
      if (!data?.length) {
        return '-';
      }
      return <QEntityLink coy-arr={data} />;
    },
    qentityObj: (data) => {
      if (!data) {
        return '-';
      }
      return <QEntityLink coy-obj={data} />;
    },
    ChattelMortgagebdyr: (item) => {
      return <QEntityLink coy-arr={item.MPledgeDetail && item.MPledgeDetail.PledgeeList}></QEntityLink>;
    },
    // 动产抵押-所有权或使用权归属
    MPledgeDetail: (item: any) => {
      if (item?.MPledgeDetail?.GuaranteeList) {
        const guishu: any = [];
        const guishukeyno: any = [];
        item.MPledgeDetail.GuaranteeList.forEach((gua: any) => {
          if (gua.KeyNoList) {
            gua.KeyNoList.forEach((knl: any) => {
              if (!guishu.includes(knl.Name)) {
                guishu.push(knl.Name);
                guishukeyno.push([knl.Name, knl.KeyNo]);
              }
            });
          }
        });
        if (guishukeyno?.length) {
          const filters = guishukeyno.filter((s, i) => i < 3);
          return filters.map((i, index) => {
            if (i[1]) {
              return (
                <div>
                  <a href={`/embed/companyDetail?keyNo=${i[1]}&title=${i[0] || ''}`} target="_blank">
                    {i[0] || '-'}
                  </a>
                  {index === 2 && '等'}
                </div>
              );
            }
            return i[0];
          });
        }
        return '-';
      }
      return '-';
    },
    // 土地抵押
    LandMortgagedyr: (item) => {
      return <QEntityLink coyObj={item.MortgagorNames && item.MortgagorNames[0]}></QEntityLink>;
    },
    LandMortgagebdyr: (item) => {
      return <QEntityLink coyObj={item.MortgagePeoples && item.MortgagePeoples[0]}></QEntityLink>;
    },
    startEndDate: (item) => {
      const start = (item.StartDate && dateFormat(item.StartDate)) || '-';
      const end = (item.EndDate && dateFormat(item.EndDate)) || '-';
      return (
        <div>
          <span>{start}</span>
          <span> 至 </span>
          <span>{end}</span>
        </div>
      );
    },
    // 行政处罚
    PenaltiesNo: (item) => {
      if (item.CaseNo) {
        return (
          <DimensionDetailAction record={item} dimensionKey={meta.key} keyNo={meta.keyNo} displayType="link">
            {item.CaseNo}
          </DimensionDetailAction>
        );
      }
      return '-';
    },
    urlAction: (item) => {
      return <DimensionDetailAction record={item} dimensionKey={meta.key} keyNo={meta.keyNo} displayType="link" />;
    },
    RegisterNo: (item) => {
      return item.RegisterNo ? <a onClick={() => vm.showDetail(meta.key, item)}>{item.RegisterNo}</a> : '-';
    },
    // 股权出质
    RegistNo: (item) => {
      if (item.RegistNo) {
        return (
          <DimensionDetailAction record={item} dimensionKey={meta.key} keyNo={meta.keyNo}>
            {item.RegistNo}
          </DimensionDetailAction>
        );
      }
      return '-';
    },
    pledgorInfo: (item) => {
      return item.PledgorInfo && item.PledgorInfo.length ? (
        <div>
          {item.PledgorInfo.map((v) => {
            return (
              <div class="flex flex-wrap" style={{ gap: '4px' }}>
                <QEntityLink coyObj={v}></QEntityLink>
                <InlineCollapse theme="grey">
                  {v.Job
                    ? getJobArr(v.Job).map((job) => {
                        return <span class={[styles.jobPosition, getJobClass(job)]}>{job}</span>;
                      })
                    : null}
                </InlineCollapse>
              </div>
            );
          })}
        </div>
      ) : (
        '-'
      );
    },
    relatedCompanyInfo: (item) => {
      return <QEntityLink coyObj={item.RelatedCompanyInfo} />;
    },
    pledgeeInfo: (item) => {
      return (
        <div>
          {item.PledgeeInfo && item.PledgeeInfo.length
            ? item.PledgeeInfo.map((v) => {
                return <QEntityLink coyObj={{ KeyNo: v.KeyNo, Name: v.Name }}></QEntityLink>;
              })
            : '-'}
        </div>
      );
    },
    // 司法拍卖
    biaoti: (item) => {
      return (
        <div>
          {item.name || '-'}
          <span>
            <div v-show={vm.getTags(item).length > 0} class="tags" style="margin-top: 5px">
              {vm.getTags(item).map((v: any, index) => {
                return (
                  <QTag type={v.type} key={index}>
                    {v.label}
                  </QTag>
                );
              })}
            </div>
          </span>
        </div>
      );
    },
    // 案号
    CaseNo: (item) => {
      let title;
      switch (meta.key) {
        case 'SalesContractDispute':
        case 'CompanyOrMainMembersCriminalInvolve':
        case 'CompanyOrMainMembersCriminalInvolveHistory':
        case 'MajorDispute':
          title = item.casename;
          break;
        default:
          title = `${item.Court || item.ExecuteGov || ''}${item.CaseNo}`;
      }
      if (['SalesContractDispute', 'MajorDispute', 'CompanyOrMainMembersCriminalInvolve'].includes(meta.key)) {
        return (
          <span>
            <a href={`/embed/judgementInfo?id=${String(item.id).slice(0, -1)}&title=${title}`} target="_blank">
              {item.CaseNo || item.caseno || '-'}
            </a>
          </span>
        );
      }

      // 被执行人信息案号弹窗处理
      if (['PersonExecution'].includes(meta.key)) {
        return <a onClick={() => vm.showDetail(meta.key, item)}>详情</a>;
      }

      const url = getDetailByType(meta.key, item);

      const name = item.CaseNo || item.caseno || item.Caseno || '-';
      if (!url || name === '-') {
        return name;
      }
      return (
        <span>
          <a href={`${url}&title=${title}`} target="_blank">
            {name}
          </a>
        </span>
      );
    },
    // 行政处罚案号，带处罚事由
    casenoWithTag: (record) => {
      const { CaseNo, punishreasontype } = record;
      if (!CaseNo) {
        return '-';
      }
      return (
        <div>
          <div> {CaseNo} </div>
          {Array.isArray(punishreasontype) && punishreasontype.length > 0 ? (
            <div class={'flex'}>
              {punishreasontype.reduce((arr, cur) => {
                const label = punishReasonTypeMap.find((op) => op.value === cur)?.label;
                if (label) {
                  arr.push(<QTag type="list">{label}</QTag>);
                }
                return arr;
              }, [])}
            </div>
          ) : null}
        </div>
      );
    },
    // 股权冻结 - 执行通知书文号
    CaseSearchId: (item) => {
      if (!item.CaseSearchId) {
        return item.CaseNo || '-';
      }
      const url = `/embed/courtCaseDetail?caseId=${item.CaseSearchId}&title=${item.CaseNo || ''}`;
      return (
        <span>
          <a href={url} target="_blank">
            {item.CaseNo || '-'}
          </a>
        </span>
      );
    },
    OrgNo: (item) => {
      if (!item.CaseSearchId) {
        return item.OrgNo || '-';
      }
      const courtName = item.Executegov || item.ExecuteGov || (Array.isArray(item.Court) ? item.Court[0] : item.Court);
      const url = `/embed/courtCaseDetail?caseId=${item.CaseSearchId}&title=${courtName}${item.OrgNo}`;
      return (
        <a href={url} target="_blank">
          {item.OrgNo || '-'}
        </a>
      );
    },
    // 限制高消费
    limitObj: (item) => {
      return (
        <div>
          <QEntityLink coyObj={{ KeyNo: item.KeyNo, Name: item.Name }}></QEntityLink>
          <span style="display: block">
            {(item.NameAndKeyNo ?? []).map((person) => {
              return person.KeyNo === item.KeyNo && person.TD ? <QRoleText v-show={person.TD} roleD={person.TD}></QRoleText> : null;
            })}
          </span>
        </div>
      );
    },
    applicant: (item) => {
      return (
        <div>{item.ApplicantInfo && item.ApplicantInfo.length ? <QEntityLink ellipsis={false} coy-arr={item.ApplicantInfo} /> : '-'}</div>
      );
    },
    originalSource: (item) => {
      return <FileLogo fileData={item} />;
    },
    // 限制出境
    limitedPerson: (item) => {
      if (item.LimitedPerson?.length) {
        return item.LimitedPerson.map((s) => {
          return (
            <QEntityLink
              coyObj={{
                Name: s.Name,
                KeyNo: s.KeyNo,
              }}
            ></QEntityLink>
          );
        });
      }
      return '-';
    },
    /**
     * 工商外链处理
     */
    entityLinks: (items) => {
      if (Array.isArray(items) && items.length > 0) {
        return items.map((item) => {
          return (
            <QEntityLink
              key={item.KeyNo}
              coyObj={{
                Name: item.Name,
                KeyNo: item.KeyNo,
              }}
            ></QEntityLink>
          );
        });
      }
      return '-';
    },
    EntityLinksAndJob: (items) => {
      if (Array.isArray(items) && items.length > 0) {
        return items.map((item) => {
          return (
            <div class="flex flex-wrap" style={{ gap: '4px' }}>
              <QSimpleEntityLink
                value={{
                  KeyNo: item.KeyNo,
                  Org: item.Org,
                  Name: item.Name,
                }}
              />
              <InlineCollapse theme="grey">
                {item.Job
                  ? getJobArr(item.Job).map((job) => {
                      return (
                        <span class={[styles.jobPosition, getJobClass(job)]} style={{ marginLeft: '0' }}>
                          {job}
                        </span>
                      );
                    })
                  : null}
              </InlineCollapse>
            </div>
          );
        });
      }
      return '-';
    },
    executedPerson: (item) => {
      return (
        <div>
          {item.ExecutedPerson && item.ExecutedPerson.length
            ? item.ExecutedPerson.map((pro, index) => {
                return (
                  <span>
                    <QEntityLink coyObj={pro}></QEntityLink>
                    {pro.TD && <QRoleText roleD={pro.TD}></QRoleText>}
                    <span v-show={index + 1 !== item.executedPerson.length}>，</span>
                  </span>
                );
              })
            : '-'}
        </div>
      );
    },
    applayer: (item) => {
      return (
        <div>
          {item.applayer && item.applayer.length
            ? item.applayer.map((pro, index) => {
                return (
                  <span>
                    <QEntityLink coyObj={pro}></QEntityLink>
                    <span v-show={item.applayer.length > 1 && index !== item.applayer.length - 1}>，</span>
                  </span>
                );
              })
            : '-'}
        </div>
      );
    },
    // 破产重整
    respondentName: (item) => {
      return (
        <div>
          {item.SubjectInfo && item.SubjectInfo.length
            ? item.SubjectInfo.map((v: any) => {
                return (
                  <div>
                    <QEntityLink coyObj={v}></QEntityLink>
                    {v.TD && <QRoleText roleD={v.TD}></QRoleText>}
                  </div>
                );
              })
            : '-'}
        </div>
      );
    },
    blacklistCompanyName: (item) => {
      return (
        <div>
          {item.Name ? (
            <QEntityLink
              coyObj={{
                Name: item.Name,
                KeyNo: item.KeyNo,
              }}
            ></QEntityLink>
          ) : (
            '-'
          )}
        </div>
      );
    },
    // 产品召回
    NameAndKeyNo: (item) => {
      return (
        <div>
          {item.NameAndKeyNo && item.NameAndKeyNo.length
            ? item.NameAndKeyNo.map((v: any) => {
                return (
                  <div>
                    <QEntityLink coyObj={v}></QEntityLink>
                  </div>
                );
              })
            : '-'}
        </div>
      );
    },
    // 食品安全不合格
    CheckResult: (item) => {
      const { ExecuteStatus = '-', AmountDesc } = item;
      const isUnqualified = ExecuteStatus === '不合格' || ExecuteStatus === 2; // 是否是不合格
      const UnqualifiedDetails = JSON.parse(AmountDesc) || []; // 不合格的详情

      return (
        <div style={{ textAlign: 'center' }}>
          <QTag type={isUnqualified ? 'danger' : 'success'}>{ExecuteStatus}</QTag>
          {isUnqualified && UnqualifiedDetails && UnqualifiedDetails.length > 0 ? (
            <div style={{ marginTop: '4px' }}>
              <DimensionDetailAction record={item} dimensionKey={meta.key} keyNo={meta.keyNo}>
                详情<QIcon type="icon-wenzilianjiantou"></QIcon>
              </DimensionDetailAction>
            </div>
          ) : null}
        </div>
      );
    },
    // 双随机抽查
    taskNo: (item) => {
      return (
        <DimensionDetailAction record={item} dimensionKey={meta.key} keyNo={meta.keyNo}>
          {item.OrgNo || '-'}
        </DimensionDetailAction>
      );
    },
    // 董监高变更前
    beforeEmployees: (item) => {
      return (
        <div>
          {item?.before?.Employees && item?.before?.Employees.length
            ? item?.before?.Employees.map((v: any) => {
                return (
                  <span style={{ marginRight: '5px' }}>
                    <QEntityLink
                      coyObj={{
                        Name: v.EmployeeName,
                        KeyNo: v.KeyNo,
                      }}
                    ></QEntityLink>
                    {`${v.Job ? ',' : ''} ${v.Job}`};
                  </span>
                );
              })
            : '-'}
        </div>
      );
    },
    // 董监高变更后
    Employees: (item) => {
      return (
        <div>
          {item.Employees && item.Employees.length
            ? item.Employees.map((v: any) => {
                return (
                  <span style={{ marginRight: '5px' }}>
                    <QEntityLink
                      coyObj={{
                        Name: v.EmployeeName,
                        KeyNo: v.KeyNo,
                      }}
                    ></QEntityLink>
                    {`${v.Job ? ',' : ''} ${v.Job}`};
                  </span>
                );
              })
            : '-'}
        </div>
      );
    },
    // 法定代表人变更后
    afterLegalPerson: (item) => {
      return item?.after ? (
        <QEntityLink
          key={item.after.KeyNo}
          coyObj={{
            Name: item.after.OperName,
            KeyNo: item.after.KeyNo,
          }}
        />
      ) : (
        '-'
      );
    },
    // 法定代表人变更前
    LegalPerson: (item) => {
      return (
        <QEntityLink
          key={item.KeyNo}
          coyObj={{
            Name: item.OperName,
            KeyNo: item.KeyNo,
          }}
        />
      );
    },
    afterScope: (item) => {
      const result = getHighLightDifferent(item?.Scope, item.after?.Scope);
      return <div class={styles.highlight} domPropsInnerHTML={result.afterContent}></div>;
    },
    afterAddress: (item) => {
      const result = getHighLightDifferent(item?.Address, item.after?.Address);
      return <div class={styles.highlight} domPropsInnerHTML={result.afterContent}></div>;
    },
    beforeScope: (item) => {
      const result = getHighLightDifferent(item?.Scope, item.after?.Scope);
      return <div class={styles.highlight} domPropsInnerHTML={result.beforeContent}></div>;
    },
    beforeAddress: (item) => {
      const result = getHighLightDifferent(item?.Address, item.after?.Address);
      return <div class={styles.highlight} domPropsInnerHTML={result.beforeContent}></div>;
    },
    parseContent: (data) => {
      const content = JSON.parse(data);
      const type = content instanceof Array;
      return type ? <QEntityLink coy-arr={JSON.parse(data)}></QEntityLink> : <QEntityLink coyObj={JSON.parse(data)}></QEntityLink>;
    },
    relatedCompanyName: (item: any) => {
      return <QEntityLink coyObj={{ KeyNo: item.relatedCompanyId, Name: item.relatedCompanyName }}></QEntityLink>;
    },
    relatedCompanyNameWithTag: (item: any) => {
      // 历史标签处理，针对黑名单的需要单独做下处理
      const getHistoryRole = () => {
        switch (meta.key) {
          case 'Shareholder':
          case 'ShareholdingRelationship':
            return '历史股东';
          case 'ForeignInvestment':
          case 'InvestorsRelationship':
            return '历史对外投资';
          default:
            return item.role;
        }
      };
      if (item.history) {
        item.role = getHistoryRole();
      }
      return (
        <span>
          <a
            style={{ marginRight: '5px', verticalAlign: 'middle' }}
            href={`/embed/companyDetail?keyNo=${item.relatedCompanyId || item.companyKeynoRelated}&title=${
              item.relatedCompanyName || item.companyNameRelated
            }`}
            target="_blank"
          >
            {item.relatedCompanyName || item.companyNameRelated}
          </a>
          {item.role && item.history ? <QTag>{item.role}</QTag> : null}
        </span>
      );
    },
    companyName: (item: any) => {
      return <QEntityLink coyObj={{ KeyNo: item.companyId, Name: item.companyName }}></QEntityLink>;
    },
    personName: (item: any) => {
      return <QEntityLink coyObj={{ KeyNo: item.personId, Name: item.personName }}></QEntityLink>;
    },
    path: (item: any) => {
      const paths = preDealConnectPath(item);
      if (paths.length > 0) {
        return <AssociationPath paths={[paths]} showTitle></AssociationPath>;
      }
      return '-';
    },
    annoAction: (item) => {
      return (
        <DimensionDetailAction record={item} dimensionKey={meta.key} keyNo={meta.keyNo}>
          {item.annoName}
        </DimensionDetailAction>
      );
    },
    action: (item) => {
      // 亚投行禁止名单不显示详情
      if (meta.key === 'HitOuterBlackList' && [100, 101, 102].includes(item.listtypecode)) {
        return '-';
      }
      if (meta.key === 'HitOuterBlackList' && item.isdetails !== 1) {
        return '-';
      }
      return <DimensionDetailAction record={item} dimensionKey={meta.key} keyNo={meta.keyNo} />;
    },
    SpotCheck: (item) => {
      const getColorByText = (value: string) => {
        if (value !== '未发现问题') {
          return '#F04040';
        }
        return 'inherit';
      };

      const data = (item.title ?? '').split('；').map((t) => t.split('：'));
      if (data.length <= 1) {
        return <span style={{ color: getColorByText(item.title) }}>{item.title || '-'}</span>;
      }

      const columns = data.map(([k, v], index) => [
        `抽查事项${index + 1}`,
        k,
        '抽查结果',
        h('span', { style: { color: getColorByText(v) } }, v || '-'),
      ]);

      return <DimensionDetailAction record={{ ...item, columns }} dimensionKey={meta.key} keyNo={meta.keyNo} />;
    },
    ReferenceNo: (record) => {
      if (!record.caseNo) {
        return '-';
      }
      return (
        <DimensionDetailAction record={record} dimensionKey={meta.key} keyNo={meta.keyNo}>
          {record.caseNo}
        </DimensionDetailAction>
      );
    },
    // 国央企采购黑名单
    govProcurementIllegalDetail: (data) => {
      if (data?.newsId) {
        return (
          <a href={`/embed/post-news?newsId=${data.newsId}&title=${data.title || data.Name}`} target="_blank">
            详情
          </a>
        );
      }
      if (data?.id && data.isdetails === 1) {
        return <DimensionDetailAction record={data} dimensionKey={'GovProcurementIllegal'} keyNo={meta.keyNo} />;
      }
      return '-';
    },
    // 终本案件 Detail
    endExecutionCaseDetail: (item) => {
      if (item.CaseSearchId) {
        return (
          <DimensionDetailAction record={item} dimensionKey={meta.key} keyNo={meta.keyNo}>
            {item.CaseNo}
          </DimensionDetailAction>
        );
      }
      return item.CaseNo || '-';
    },
    atlasAction: (text, record) => {
      return <a onClick={() => openAtlasChartDialog(record)}>图谱</a>;
    },
    // 双随机抽查详情
    drcDetail: (data) => {
      return <DimensionDetailAction record={{ ids: data.Id, keyNo: meta.keyNo }} dimensionKey={'drc'} keyNo={meta.KeyNo} />;
    },
    // 风险等级
    riskLevel: (level?: number) => {
      if (level === undefined || !RISK_LEVEL_CODE_THEME_MAP[level]) {
        return '-';
      }
      const schema = RISK_LEVEL_CODE_THEME_MAP[level];
      return <QTag style={getRiskLevelStyle(level)}>{schema.label}</QTag>;
    },
    // 案号
    AnNoList: (item) => {
      const url = `/embed/courtCaseDetail?caseId=${item.CaseSearchId || item.Id}&title=${item.CaseName}`;
      return (
        <ClampContent clampKey={item.Id + meta.tkey}>
          <a href={url} target="_blank" style={{ whiteSpace: 'pre' }}>
            {item.AnNoList.join('\n')}
          </a>
        </ClampContent>
      );
    },
    // 最新案件进程
    LastCaseProgress: (item) => {
      return (
        <div>
          <div>{item.LastestDate ? moment(item.LastestDate * 1000).format('YYYY-MM-DD') : '-'}</div>
          <div>{item.LatestTrialRound}</div>
        </div>
      );
    },
    CourtList: (item) => {
      return <div domPropsInnerHTML={item?.CourtList?.join('<br />')}></div>;
    },
    PersonNo: (item) => {
      // 关联人的样式
      const formRelate = () => {
        if (!item.relationPersonKeyNo && !item.relationPersonName) return '';
        if (item.relationPersonKeyNo) {
          return (
            <span>
              <a href={`/embed/beneficaryDetail?personId=${item.relationPersonKeyNo}&title=${item.relationPersonName}`} target="_blank">
                {item.relationPersonName}
              </a>
              {'—'}
            </span>
          );
        }
        return (
          <span>
            {item.relationPersonName}
            {'—'}
          </span>
        );
      };
      const getPNO = () => {
        const pNo = item.personNo;
        if (item.relationPersonId && item.relationPersonId !== -1) {
          const name = pNo.split(`_${item.relationship}`)[0];
          return (
            <span>
              <span>{name}</span>
              {'_'}
              {formRelate()}
              <span>{item.name}</span>
              <span>（{item.relationship}）</span>
            </span>
          );
        }
        return (
          <span>
            {pNo}_{item.name}
          </span>
        );
      };
      return (
        <span class="flex items-center" style={{ gap: '5px' }}>
          {getPNO()}
          {item.status === 1 ? (
            <QTag type="danger" style={{ color: '#666' }}>
              是本人
            </QTag>
          ) : null}
        </span>
      );
    },
    ActionCheck: (item) => {
      return <SuspectInterestConflictVerify record={item} vm={vm} />;
    },
    CaseIdentity: (item) => {
      let roleRL = [];
      if (item.CaseRoleSearch) {
        const arr = JSON.parse(item.CaseRoleSearch);
        if (arr?.length) {
          arr.forEach((role) => {
            if (role.N === vm.meta.keyNo || role.P === vm.meta.companyName) {
              roleRL = role.RL;
            }
          });
        }
        if (roleRL?.length) {
          const roles = roleRL.map((rm: any) => {
            const ldr = LAWSUIT_RESULT_CODE_MAP[rm.LR];
            return (
              <div>
                {rm.T}
                {rm.R}
                {rm.LR ? <span style={{ ...getColor(ldr) }}>[{ldr}]</span> : null}
              </div>
            );
          });
          if (item?.SeriesGroupCount > 1) {
            roles.push(
              <span class={[' m-t-xs ntag-v2 ntag-v2-primary', styles.actionWrapper]}>
                全部<span style={{ color: 'red' }}>&nbsp;{item.SeriesGroupCount}&nbsp;</span>个系列案件
              </span>
              // <DimensionDetailAction
              //   class={styles.actionWrapper}
              //   record={{ ...item, Name: meta.companyName }}
              //   dimensionKey={meta.key}
              //   keyNo={meta.keyNo}
              // >
              // </DimensionDetailAction>
            );
          }
          return roles;
        }
      }
      return '-';
    },

    // 当事人
    Parties: (item) => {
      // 获取当事人信息
      const getParties = () => {
        let rolesObj: Record<string, any> = {};
        if (item.caserolegroupbyrolename?.length) {
          rolesObj = item.caserolegroupbyrolename
            ?.filter((item2) => item2.LawyerTag === 0 || isNil(item2.LawyerTag))
            .reduce((obj, { Role, DetailList }) => {
              obj[Role] = DetailList;
              return obj;
            }, {});
        } else if (item.caserole?.length) {
          rolesObj = item.caserole.reduce((obj, roleObj) => {
            const { R } = roleObj;
            if (obj[R]) {
              obj[R].push(roleObj);
            } else {
              obj[R] = [roleObj];
            }
            return obj;
          }, {});
        }
        if (item.involveRole) {
          item.involveRole.forEach((role) => {
            if (rolesObj[role.Tag]) {
              rolesObj[role.Tag].push(role);
            } else {
              rolesObj[role.Tag] = [role];
            }
          });
        }
        return rolesObj;
      };
      const parties = getParties();

      return renderNameList(parties as any, { name: ['ShowName', 'Name'], no: 'KeyNo' });
    },
    // 当事人 RA-1779
    RoleAmt: (item: any) => {
      const entities = item?.RoleAmt || [];
      if (!entities.length) {
        return '-';
      }

      return (
        <ClampContent clampKey={item.Id + meta.tkey}>
          {entities.map((entity: Record<string, any>, index: number) => {
            const findTag = item.involveRole?.find((role) => role.KeyNo === entity.N);
            return (
              <div class="flex" style={{ gap: '5px' }} key={`${entity.N}-${index}`}>
                <span style={{ whiteSpace: 'nowrap' }}>{entity.R}</span>
                <span>-</span>
                <QSimpleEntityLink
                  style="margin-left: 4px;"
                  value={{
                    KeyNo: entity.N,
                    Org: entity.O,
                    Name: entity.P,
                  }}
                />
                {findTag?.Tag ? <QTag type="warning">{findTag.Tag}</QTag> : null}
                {/* 存在多个角色，需要分开展示 */}
                {entity.Job && (
                  <div class={styles.jobbox}>
                    {entity.Job.split(',').map((job) => {
                      return <span class={[styles.jobPosition, getJobClass(job)]}>{job}</span>;
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </ClampContent>
      );
    },
    caseName: (item: Record<string, any>) => {
      const caseName = item.casename || item.CaseNameClean || item.CaseName;
      const involveTags = item.involveTags || item.CaseTypeArray || item.CaseType || [];
      if (!caseName) {
        return '-';
      }
      return (
        <div>
          <span style={{ marginRight: '4px' }}>{caseName}</span>
          {Array.isArray(involveTags) && involveTags.length > 0
            ? (involveTags || []).map((tagText, index) => {
                return (
                  <QTag style={{ marginTop: '-1px' }} type="default" key={`${tagText}-${index}`}>
                    {tagText}
                  </QTag>
                );
              })
            : null}
        </div>
      );
    },
    controllerAtlasAction: (text, record) => {
      return (
        <Button
          type="link"
          onClick={() => {
            window.open(
              `/embed/charts/actualcontroller?keyNo=${record.companyId}&ac=undefined&companyName=${record.companyName}&rc=1&show=1,3,2,5,6,11,12,7,9,21,4&allcharts=1&title=${record.companyName}`
            );
          }}
        >
          图谱
        </Button>
      );
    },
    detailPath: (scope) => preDealControlPath(scope, meta),

    // 疑似潜在利益冲突（人员）
    interestConflictPerson: (item) => {
      const nodes: VNode[] = [];

      // 相同姓名
      if (item.isSameName) {
        const name = item.name || item.Name;
        const keyNo = item.keyNo || item.KeyNo || '';
        // 需要判断是否为企业
        const isPerson = keyNo.startsWith('p');
        const url = isPerson
          ? `/embed/beneficaryDetail?personId=${keyNo}&title=${name}`
          : `/embed/companyDetail?keyNo=${keyNo}&title=${name}`;

        const node = keyNo ? (
          <a href={url} target="_blank">
            {name}
          </a>
        ) : (
          <span>{name}</span>
        );
        nodes.push(
          <div>
            <span>疑似同名</span>
            <span>（{node}）</span>
          </div>
        );
      }

      // 相同联系方式
      if (item.isSameContact) {
        const contacts: string[] = [];
        if (!isEmpty(item.phones)) {
          const phones = item.phones.map(({ n, t }) => `${n} ${t}`);
          contacts.push(...phones);
        }
        if (!isEmpty(item.emails)) {
          const emails = item.emails.map(({ e }) => e);
          contacts.push(...emails);
        }

        if (!isEmpty(contacts)) {
          nodes.push(
            <div>
              <span>相同联系方式</span>
              <span>（{contacts.join('，')}）</span>
            </div>
          );
        }
      }

      return <div>{nodes}</div>;
    },

    // 关联关系
    relateType: (scope, record, index, config) => {
      const { _showReason } = config;
      // 交叉重叠关系&内部黑名单支持穿透3层关系
      if (scope.version === 'V2') {
        return preDealRowTemplateV2(vm, scope, _showReason);
      }
      // 疑似关系，额外处理，拎出来处理更容易理解
      if (['CustomerSuspectedRelation', 'BlacklistSuspectedRelation'].includes(meta.key)) {
        return preDealSuspectedRelation(scope, _showReason, vm);
      }
      // 老数据1层穿透
      return preDealRowTemplate(scope, _showReason);
    },
    BeforeChange: (scope) => {
      const ChangeExtend = JSON.parse(scope.ChangeExtend)[0];
      const BeforeContent = ChangeExtend.BeforeContent ? JSON.parse(ChangeExtend.BeforeContent) : '';
      if (!BeforeContent) {
        return '-';
      }
      const type = BeforeContent instanceof Array;
      if (type) {
        return (
          <div>
            {BeforeContent.map((content) => {
              return renderBenifit(content);
            })}
          </div>
        );
      }
      return renderBenifit(BeforeContent);
    },

    AfterChange: (scope) => {
      const ChangeExtend = JSON.parse(scope.ChangeExtend)[0];
      const AfterContent = ChangeExtend.AfterContent ? JSON.parse(ChangeExtend.AfterContent) : '';
      if (!AfterContent) {
        return '-';
      }
      const type = AfterContent instanceof Array;
      if (type) {
        return (
          <div>
            {AfterContent.map((content) => {
              return renderBenifit(content);
            })}
          </div>
        );
      }
      return renderBenifit(AfterContent);
    },

    /**
     * FIXME: 通过配置统一处理对象所需的字段
     */
    companyNamePascal: (item: any) => {
      return <QEntityLink coyObj={{ KeyNo: item.KeyNo, Name: item.CompanyName }}></QEntityLink>;
    },

    // 政府公告 Detail
    govNoticeDetail: (id?: string, item?: any) => {
      if (!id) {
        return '-';
      }
      return (
        <a href={`/embed/govnoticeDetail?id=${id}&title=${item?.title || ''}`} target="_blank">
          详情
        </a>
      );
    },
    // 股权冻结被执行人数据源更换
    pledgName: (text, record) => {
      return pledgCompanyChange(text, record, 'SubjectInfo');
    },
    // 股权冻结冻结股权标的企业数据源更换
    pledgPledgor: (text, record) => {
      return pledgCompanyChange(text, record, 'PledgorInfo');
    },
    // 内容过长的时候折叠
    clampcontent: (text, row) => {
      return (
        <ClampContent clampKey={row.CreditCode || row.Id}>
          <span domPropsInnerHTML={text || '-'}></span>
        </ClampContent>
      );
    },
    taxOffice: (list: unknown[]) => {
      if (Array.isArray(list) && list.length > 0) {
        return (
          <div>
            {list.map((el) => (
              <div>
                <q-entity-link coy-obj={el} />
              </div>
            ))}
          </div>
        );
      }
      return '-';
    },
    overdueStatus: (data) => {
      if (data) {
        return <QTag type={data.Code === '3' ? 'danger' : 'gray'}>{data.Desc}</QTag>;
      }
      return '-';
    },

    concernedParties: (record) => {
      const caseRoleGroup = record.caseRoleGroup || [];
      // 里面可能不止原被告，不能用R === 0判断，先按角色排序，再用reduce分类
      const RolesArrMap = sortBy(caseRoleGroup, 'R').reduce((acc, cur) => {
        if (acc[cur.RN]) {
          acc[cur.RN].push(cur);
        } else {
          acc[cur.RN] = [cur];
        }
        return acc;
      }, {});
      return renderNameList(RolesArrMap, { name: ['ShowName', 'P'], no: 'N' });
    },
    blackPath: (record) => {
      record.relations.forEach((elements) => {
        const lineData = elements.filter((item) => item.type === 'edge');
        lineData.forEach((ld) => {
          if (['HisEmploy', 'HisLegal', 'HisInvest'].includes(ld.roleType)) {
            ld.data = ld.roles.map((sr) => ({
              data: ld.roles.filter((rd) => rd === sr),
              role: sr,
              type: ld.roleType,
              endid: ld.endid,
              startid: ld.startid,
              direction: ld.direction === 'left' ? -1 : 1,
            }));
          }
        });
      });
      return (
        <LineDrawer
          record={record}
          dataKey={'relations'}
          showTag={false}
          onEdgeClick={(data) => {
            vm.$modal.showDimension('ControlRelation', data);
          }}
        />
      );
    },
    blackRenderAtlasChart: (record) => {
      return (
        <a
          onClick={() => {
            const data = parseGraphData([record]);
            openRelationalChartDialog(data);
          }}
        >
          图谱
        </a>
      );
    },
    IPRPledgePeriod: (record) => {
      const { PledgeStartDate, PledgeEndDate } = record;
      const startDate = transDate(PledgeStartDate);
      const endDate = transDate(PledgeEndDate);
      if (startDate === '-' && endDate === '-') {
        return '-';
      }
      return `${startDate} 至 ${endDate}`;
    },
    caseReason: (reason, record) => {
      if (!reason) {
        return '-';
      }
      return (
        <div style={{ minWidth: '120px' }}>
          {reason}
          {record.caseReasonDescription ? <QGlossaryInfo tooltip={record.caseReasonDescription}></QGlossaryInfo> : null}
        </div>
      );
    },
    certificationName: (record) => {
      const { certificationId, name } = record;
      if (certificationId)
        return (
          <a
            onClick={() =>
              openDetailModal({
                Id: certificationId,
                CertificateCodeDesc: name,
              })
            }
          >
            {name}
          </a>
        );
      return name || '-';
    },
    fakeSoeName: (reccord) => {
      const isSelf = reccord.riskType === 'self';
      return (
        <div>
          <q-entity-link
            coyObj={{
              Name: reccord.riskCompanyName,
              KeyNo: reccord.riskCompanyId,
            }}
          />
          {isSelf && (
            <span
              style={{
                display: 'inline-block',
                lineHeight: '20px',
                padding: '0px 4px',
                borderRadius: '2px',
                marginLeft: '5px',
                ...getStyleByJob('企业主体'),
              }}
            >
              企业主体
            </span>
          )}
        </div>
      );
    },
    fakePath: (record) => {
      if (!record.relations?.relations) {
        return '-';
      }
      return <LineDrawer showTag={false} record={record.relations} dataKey={'relations'} />;
    },
    entityLink: (val, record, index, column) => {
      const { entity } = column;
      if (!entity) {
        return '-';
      }
      const KeyNo = get(record, entity.keyNo);
      const Name = get(record, entity.name);
      if (!KeyNo || KeyNo === '-') {
        return '-';
      }
      return <QEntityLink coyObj={{ KeyNo, Name }} />;
    },
  };
};
