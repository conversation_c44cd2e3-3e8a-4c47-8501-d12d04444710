// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`InterestInvestigationBatchBatchPage > 测试组件是否正确渲染 1`] = `
<hero-layout-stub>
  <div class="root">
    <div class="header">
      <div class="wrapper border">
        <div class="title">
          <div class="container"></div>
        </div>
      </div>
    </div>
    <div class="body">
      <div class="root">
        <div class="body" style="padding: 30px;">
          <h2 class="text-center" style="font-size: 32px; line-height: 1.5; margin-bottom: 20px;">Interest Relation Screening Batch</h2>
          <div class="flex" style="margin: 0px auto; max-width: 910px;">
            <div class="container" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
              <div class="box">
                <div class="title">上传文档</div><span style="height: 134px;" class="container lighter"><div class="ant-upload ant-upload-drag"><!----></div></span>
              </div>
              <div class="description">
                <ul>
                  <li><a href="https://qcc-static.qcc.com/rover/public/templates/import/批量特定利益关系项目导入模版.xlsx" download="上传文件模板.xlsx" class="container">
                      <q-icon-stub type="icon-xiazai"></q-icon-stub><span>下载模板</span>
                    </a><span> 并按照样式编辑好数据，切勿增减列</span></li>
                  <li>上传文件不超过2M，仅支持Excel</li>
                  <li>批量特定利益关系排查一次支持最多不超过&nbsp;<em>1000</em>&nbsp; 个项目，单个项目排查企业数不超过&nbsp;<em>0</em>&nbsp; 家</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="ant-spin-nested-loading">
    <div class="ant-spin-container">
      <div class="guide">
        <div class="imgWrapper"><img src="/src/assets/images/interest-batch.png" alt="guide" width="844"></div>
        <div class="desc">
          <div style="white-space: nowrap;">功能说明：</div>
          <div>
            <p>1、特定利益关系排查采用先进的大数据处理技术，挖掘特定利益关系上下游之间的交叉持股、控制关系、相同人员以及相同经营地址、相同联系方式、相同担保关联等关联关系，识别潜在虚假贸易的可能性，协助企业高效防控虚假贸易业务；</p>
            <p>2、批量特定利益关系排查，是针对上传项目对应的企业名录进行以下检查:a.上下游为母子公司或由相同的实际控制人控制;b.上下游企业交叉持股;c.上下游企业主要负责人、董事、监事、高级管理人员相同;d.上下游企业注册地址、实际办公地址、业务联系人或联系电话相同;e.上下游企业一方为另一方贸易合同履约提供担保；f.上下游企业存在长期业务关系，一方为另一方的重要供应商或特约经销商；g.其他根据实质重于形式原则认定存在特定利益关系的情形，如股权出质关联、动产抵押关联等；</p>
            <p>3、批量特定利益关系排查，最多同时支持<em>1000</em>个项目进行排查，单个项目排查企业数不超过<em>10</em>家。</p>
          </div>
        </div>
      </div>
      <div class="root" scroll="[object Object]" style="display: none;">
        <div class="header">
          <div class="wrapper border">
            <div class="title">批量排查任务</div>
            <div class="extra">
              <div></div>
            </div>
          </div>
        </div>
        <div class="body" style="padding: 15px;">
          <div class="container">
            <div class="ant-spin-nested-loading">
              <div>
                <div class="ant-spin ant-spin-spinning"><span class="ant-spin-dot ant-spin-dot-spin"><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i><i class="ant-spin-dot-item"></i></span></div>
              </div>
              <div class="ant-spin-container ant-spin-blur">
                <div class="ant-table-wrapper table" emptytext="暂时没有找到相关数据" scrolltofirstrowonchange="true">
                  <div class="ant-spin-nested-loading">
                    <div class="ant-spin-container">
                      <div class="ant-table ant-table-fixed-header ant-table-scroll-position-left ant-table-layout-fixed ant-table-default ant-table-bordered ant-table-empty">
                        <div class="ant-table-content">
                          <div class="ant-table-scroll">
                            <div class="ant-table-header">
                              <table class="ant-table-fixed" style="width: 1200px;">
                                <colgroup>
                                  <col style="width: 58px; min-width: 58px;">
                                </colgroup>
                                <thead class="ant-table-thead">
                                  <tr>
                                    <th key="0" align="left" class="ant-table-align-left ant-table-row-cell-break-word ant-table-row-cell-last" style="text-align: left;"><span class="ant-table-header-column"><div><span class="ant-table-column-title">序号</span><span class="ant-table-column-sorter"></span>
                            </div></span></th>
                            </tr>
                            </thead>
                            </table>
                          </div>
                          <div tabindex="-1" class="ant-table-body" style="overflow-x: scroll; max-height: calc(100vh - 240px - 0px); overflow-y: scroll;">
                            <table class="ant-table-fixed" style="width: 1200px;">
                              <colgroup>
                                <col style="width: 58px; min-width: 58px;">
                              </colgroup>
                              <tbody class="ant-table-tbody"></tbody>
                            </table>
                          </div>
                          <div class="ant-table-placeholder">
                            <div class="empty"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
</hero-layout-stub>
`;
