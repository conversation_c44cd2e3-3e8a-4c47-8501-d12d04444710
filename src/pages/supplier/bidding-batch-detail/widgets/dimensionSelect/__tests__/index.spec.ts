import { mount } from '@vue/test-utils';

import Dimensionselect from '../index';

vi.mock('@/shared/composables/use-company-store', () => ({
  useDimensions: vi.fn(() => ({
    tenderTypeMap: {
      type1: 'Type 1',
      type2: 'Type 2',
    },
  })),
}));

describe('Dimensionselect', () => {
  test('renders correctly with no data', () => {
    const wrapper = mount(Dimensionselect);

    expect(wrapper.html()).toBe('');
  });

  test('renders correctly with dimensions', () => {
    const wrapper = mount(Dimensionselect, {
      propsData: {
        dimensions: [
          { key: 'type1', totalHits: 5 },
          { key: 'type2', totalHits: 0 },
        ],
        currentLevel: 'type1',
      },
    });

    expect(wrapper.find('[data-testid="subDimensionBlock"]').exists()).toBe(true);
    expect(wrapper.findAll('[data-testid="subDimensionBlockItem"]').length).toBe(1);
  });

  test('renders null when no dimensions are provided', () => {
    const wrapper = mount(Dimensionselect, {
      propsData: {
        dimensions: [],
      },
    });

    expect(wrapper.html()).toBe('');
  });

  test('emits subChange event when a dimension item is clicked and is not disabled', async () => {
    const wrapper = mount(Dimensionselect, {
      propsData: {
        dimensions: [
          { key: 'type1', totalHits: 5 },
          { key: 'type2', totalHits: 0 },
        ],
      },
    });

    await wrapper.find('[data-testid="subDimensionBlockItem"]').trigger('click');
    expect(wrapper.emitted().subChange).toBeTruthy();
    expect(wrapper.emitted().subChange[0]).toEqual(['type1']);
  });

  test('does not emit subChange event when a dimension item is clicked and is disabled', async () => {
    const wrapper = mount(Dimensionselect, {
      propsData: {
        dimensions: [
          { key: 'aaa', totalHits: 5 },
          { key: 'type1', totalHits: 5 },
          { key: 'type2', totalHits: 0 },
        ],
      },
    });
    expect(wrapper.findAll('[data-testid="subDimensionBlockItem"]').at(0).text()).toContain('全部');
  });
});
