import { defineComponent, ref } from 'vue';
import { assign, cloneDeep, isEmpty, sortBy } from 'lodash';

import { openSettingModal } from '@/apps/setting/pages/investigation-setting/widgets/risk-settings/setting-modal';
import { useMonitorSettingStore } from '@/shared/composables/use-monitor-setting-store';
import RiskSwitch from '@/apps/setting/components/risk-switch';
import RiskTable from '@/apps/setting/components/risk-table';

import styles from './risk-settings.module.less';
import { TABLE_COLUMNS } from './risk-settings.config';
import { message } from 'ant-design-vue';

const RiskMonitorSettings = defineComponent({
  name: 'RiskMonitorSettings',
  props: {
    value: {
      type: Object,
      required: true,
    },
    riskKey: {
      type: String,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'edit',
    },
  },
  setup(props, { emit }) {
    const visible = ref(false);
    const loading = ref(false);
    const { isEdit, flatSettings } = useMonitorSettingStore();

    // 最后一个父级分类被关闭
    const isRootClosed = () => {
      const parentNode = flatSettings.value.filter((v) => !v.parentKey && v.key !== props.value.key);
      return parentNode.every((v) => v.status === 0);
    };

    // 最后一个子维度被关闭
    const isChildrenClosed = ({ key, status }) => {
      return props.value.items.filter((v) => v.key !== key).every((v) => v.status === 0) && status === 1;
    };

    const updateParent = (status: 0 | 1) => {
      // emit('change', { ...props.value, status });
      assign(props.value, { status });
    };

    const toggleParentSetting = ({ status }) => {
      if (status === 0 && isRootClosed()) {
        message.warning('请至少开启一项风险动态事项设置');
        return;
      }
      updateParent(status);
    };

    /**
     * 打开编辑窗口
     * @param item
     * @param dimensionKey 特殊定义 key, 用于特殊场景下标记或纠正当前确切维度Key，例如：外部黑名单
     */
    const updateRisk = async (item) => {
      const data = await openSettingModal({
        riskKey: props.riskKey,
        data: item,
        modelType: 'cooporative-monitor-setting',
        status: props.value.status,
        warningMsg: '请至少开启一项风险动态事项设置',
      });
      if (!isEmpty(data)) {
        assign(item, data);
      }
    };

    const handleSwitch = (item) => {
      console.log('handleSwitch', item);
      if (isChildrenClosed(item) && isRootClosed()) {
        message.warning('请至少开启一项风险动态事项设置');
        return;
      }
      if (isChildrenClosed(item)) {
        updateParent(0);
      }
      item.status = item.status === 0 ? 1 : 0;
    };

    /**
     * 获取表格列配置
     */
    const getColumnsByKey = (key?: string) => {
      const columns = cloneDeep(TABLE_COLUMNS);
      if (props.type === 'show') {
        return columns.slice(1, -1);
      }
      return columns;
    };

    /**
     * sortdata
     * props.value.items.filter(({ isHidden }) => isHidden) z这里是保留不显示的维度
     */

    const sortData = (v) => {
      console.log(
        'sortdata: ',
        v.map((v) => v.name)
      );
      // const sortedList = props.value.items.map((item) => {
      //   return {
      //     ...item,
      //     sort: v.findIndex((data) => data.key === item.key) + 1,
      //   };
      // });
      // emit('change', {
      //   ...props.value,
      //   items: sortedList,
      // });
      // assign(props.value, { items: sortedList });
      props.value.items.forEach((item) => {
        item.sort = v.findIndex((data) => data.key === item.key) + 1;
      });
    };

    return {
      visible,
      isEdit,
      handleSwitch,
      updateRisk,
      flatSettings,
      loading,
      getColumnsByKey,
      sortData,
      toggleParentSetting,
    };
  },
  render() {
    const { riskKey, value } = this;
    const filterName = value.name;
    // 过滤隐藏的风险项 `isHidden: true`
    const valueItems = value.items?.filter(({ isHidden }) => !isHidden) || [];
    const renderRiskSettings = (arr: any[]) => {
      if (value.status === 0) return null;
      const sortedList = sortBy(
        arr.filter((item) => !item.isHidden),
        'sort'
      );
      return (
        <div class={styles.body}>
          <RiskTable
            disabled={value.status === 0 || this.disabled}
            dataSource={sortedList}
            isEdit={this.isEdit}
            columns={this.getColumnsByKey(riskKey)}
            onEdit={this.updateRisk}
            onSort={this.sortData}
            onSwitch={this.handleSwitch}
            loading={this.loading}
          />
        </div>
      );
    };

    return (
      <div class={styles.container}>
        {/* 一级维度 */}
        <div class={styles.header}>
          <RiskSwitch
            disabled={this.disabled}
            name={filterName}
            riskKey={riskKey}
            value={value.status}
            onChange={this.toggleParentSetting}
          />
        </div>

        {/* 二级维度 */}
        {renderRiskSettings(valueItems)}

        {/* 设置弹窗 */}
      </div>
    );
  },
});

export default RiskMonitorSettings;
