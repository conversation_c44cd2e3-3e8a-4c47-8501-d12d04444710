// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`RiskMonitorPage > 正常路径测试 1`] = `
<hero-layout-stub loading="true">
  <div class="root">
    <div class="body" style="padding-top: 0px;">
      <div class="container">
        <div class="main">
          <div class="root offset q-filter__root">
            <div class="group block q-filter-group--groupIds q-filter-group">
              <div class="label q-filter-label" style="width: auto; padding-top: 0px;">所属分组</div>
              <div class="wrap q-filter-wrap--groupIds">
                <q-button-select multiple="true" options=""></q-button-select>
                <div class="flex items-center"></div>
              </div>
            </div>
            <div class="group block q-filter-group--filters q-filter-group lastGroup">
              <div class="label q-filter-label" style="width: auto; padding-top: 0px;">筛选条件</div>
              <div class="wrap wrapGroups q-filter-wrap--filters">
                <div class="flex items-center flex-wrap" style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;">
                  <q-select multiple="true" options="" class="select"></q-select>
                  <q-select multiple="true" multi-column="true" options="" class="select"></q-select>
                  <q-select multiple="true" options="" class="select"></q-select>
                  <q-select multiple="true" options="" class="select"></q-select>
                  <q-cascader multiple="true" options="[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object]" expandtrigger="hover" class="select"></q-cascader>
                  <q-cascader multiple="true" options="[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object],[object Object]" expandtrigger="hover" class="select"></q-cascader>
                  <q-select multiple="true" options="[object Object],[object Object],[object Object],[object Object],[object Object]" custom="[object Object]" class="select"></q-select>
                  <q-select options="[object Object],[object Object],[object Object],[object Object],[object Object],[object Object]" custom="[object Object]" class="select"></q-select>
                  <q-select multiple="true" options="" class="select"></q-select>
                  <q-select options="[object Object],[object Object],[object Object],[object Object],[object Object],[object Object]" custom="[object Object]" class="select"></q-select>
                  <div style="display: inline-block;">
                    <div class="container tiny-search__container">
                      <div class="default tiny-search__default">
                        <div class="input">
                          <q-icon-stub type="icon-sousuo"></q-icon-stub><span>搜索</span>
                        </div>
                      </div>
                      <div class="search" style="width: 320px; display: none;">
                        <div class="inputWrapper">
                          <ainput-stub prefixcls="ant-input" placeholder="请输入企业名称或统一社会信用代码" type="text" addonafter="[object Object]" allowclear="true" lazy="true" classname="ant-input-search ant-input-search-enter-button" class="input tiny-search__input"></ainput-stub>
                        </div>
                        <div class="clear" style="display: none;"><a class="tiny-search__cancel">取消</a></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex items-center">
                  <div style="display: inline-block;">
                    <div class="aside" style="display: none;"><button type="button" class="ant-btn ant-btn-link">
                        <q-icon-stub type="icon-chexiaozhongzhi"></q-icon-stub><span>重置筛选</span>
                      </button></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="root" industrymap="[object Object]" scroll="[object Object]">
    <div class="header">
      <div class="wrapper border">
        <div class="title">
          <div class="container"><span>共找到<localereceiver-stub componentname="Icon" style="display: none;"><i aria-label="undefined: sync" class="anticon anticon-sync"><antdicon-stub type="sync-o" focusable="false" class="anticon-spin"></antdicon-stub></i></localereceiver-stub><em>0</em>条相关结果</span></div>
        </div>
        <div class="extra">
          <div>
            <div>
              <div class="extra">
                <div class="content">
                  <div class="flex-between">
                    <div>剩余可监控企业数<div class="container inline" style="margin-top: 2px;">
                        <atooltip-stub trigger="hover" placement="bottom" transitionname="zoom-big" overlaystyle="[object Object]" prefixcls="ant-popover" mouseenterdelay="0.1" mouseleavedelay="0.1" autoadjustoverflow="true" align="[object Object]"><template>
                            <div>
                              <div class="ant-popover-inner-content">
                                <div class="content">不包含已分配至其他部门的额度</div>
                              </div>
                            </div>
                          </template>
                          <div class="trigger trigger">
                            <q-icon-stub type="icon-a-shuomingxian"></q-icon-stub>
                          </div>
                        </atooltip-stub>
                      </div>：</div>
                    <div><span data-testid="remaining-count">0</span></div>
                  </div>
                </div>
                <!---->
                <wave-stub><button type="button" class="ant-btn"><span>移动分组</span></button></wave-stub>
                <wave-stub><button type="button" class="ant-btn ant-btn-primary">
                    <aicon-stub type="plus-circle"></aicon-stub><span>添加企业</span>
                  </button></wave-stub>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="body" style="padding: 15px;">
      <div class="container" style="min-height: calc(100vh - 315px);"><img src="/src/components/global/q-rich-table/components/empty/images/empty.png" width="100px" height="100px">
        <div class="description">
          <div>
            <div>暂时没有找到相关数据</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</hero-layout-stub>
`;
