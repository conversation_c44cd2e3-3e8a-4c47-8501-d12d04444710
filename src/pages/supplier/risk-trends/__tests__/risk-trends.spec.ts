import { mount, shallowMount } from '@vue/test-utils';

import { customer, riskMonitor } from '@/shared/services';
import { useAbility } from '@/libs/plugins/user-ability';
import { syncUsage } from '@/hooks/use-sync-usage-hook';
import { useCommonSettingStore } from '@/hooks/use-common-settings';
import { openCompanyModal } from '@/pages/supplier/risk-trends/widgets/add-company-modal';

import { openGroupModal } from '../hooks';
import RiskMonitorPage from '../risk-monitor';

vi.mock('@/hooks/use-common-settings');
vi.mock('@/shared/services');
vi.mock('@/libs/plugins/user-ability');
vi.mock('@/hooks/use-sync-usage-hook');
vi.mock('@/config/tracking-events', () => ({
  useTrack: () => vi.fn(),
  createTrackEvent: vi.fn(),
}));
vi.mock('../hooks');
vi.mock('../widgets/add-company-modal', () => ({
  openCompanyModal: vi.fn(),
}));

describe('RiskMonitorPage', () => {
  const baseData = {
    pagination: {
      current: 1,
      pageSize: 10,
    },
    dataSource: [{ id: 1 }, { id: 2 }, { id: 3 }],
    selectedIds: { value: [] },
    selectRows: { value: [] },
  };
  beforeEach(() => {
    vi.mocked<any>(useAbility).mockReturnValue({ check: vi.fn(() => true) });
    vi.mocked(syncUsage).mockImplementation(() => Promise.resolve());
    vi.mocked<any>(useCommonSettingStore).mockReturnValue(baseData);
    vi.mocked(riskMonitor).getCompany.mockResolvedValue({
      data: [],
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('正常路径测试', async () => {
    const wrapper = shallowMount(RiskMonitorPage);
    await wrapper.vm.$nextTick();
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('处理删除操作 - 批量删除', async () => {
    const wrapper = mount(RiskMonitorPage);
    await wrapper.vm.$nextTick();
    await wrapper.vm.handleDelete({ ids: [1, 2, 3], key: 'batch' });
    expect(riskMonitor.removeCompany).toHaveBeenCalledWith({ ids: [1, 2, 3] });
  });

  it('处理删除操作 - 全部删除', async () => {
    const wrapper = mount(RiskMonitorPage);
    await wrapper.vm.$nextTick();
    await wrapper.vm.handleDelete({ ids: [], key: 'all' });
    expect(riskMonitor.removeCompany).toHaveBeenCalled();
  });

  it('处理恢复监控操作', async () => {
    const wrapper = mount(RiskMonitorPage);
    await wrapper.vm.$nextTick();
    vi.mocked(riskMonitor.reMonitorCompany).mockResolvedValue('ok');
    await wrapper.vm.handleResume({ id: 1 });
    expect(riskMonitor.reMonitorCompany).toHaveBeenCalled();
  });

  it('处理导出操作', async () => {
    const wrapper = mount(RiskMonitorPage);
    await wrapper.vm.$nextTick();
    await wrapper.vm.handleExport();
    expect(customer.export).toHaveBeenCalled();
  });

  it('处理按ID导出操作 - 未选择记录', async () => {
    const wrapper = mount(RiskMonitorPage);
    await wrapper.vm.$nextTick();
    await wrapper.vm.handleExportByIds();
    expect(customer.export).not.toHaveBeenCalled();
  });

  it('处理按ID导出操作 - 已选择记录', async () => {
    vi.mocked<any>(useCommonSettingStore).mockReturnValue({
      ...baseData,
      selectedIds: {
        value: [1, 2, 3],
      },
    });
    const wrapper = mount(RiskMonitorPage);
    await wrapper.vm.$nextTick();
    await wrapper.vm.handleExportByIds();
    expect(customer.export).toHaveBeenCalledWith({ ids: [1, 2, 3] });
  });

  it('处理添加企业操作', async () => {
    const wrapper = mount(RiskMonitorPage);
    await wrapper.vm.$nextTick();
    await wrapper.vm.handleAddCompany();
    expect(openCompanyModal).toHaveBeenCalled();
  });

  it('处理移动分组操作', async () => {
    vi.mocked(openGroupModal).mockResolvedValue({
      groupId: 1,
    });
    vi.mocked<any>(useCommonSettingStore).mockReturnValue({
      ...baseData,
      selectedIds: {
        value: [1, 2, 3],
      },
    });
    const wrapper = mount(RiskMonitorPage);
    await wrapper.vm.$nextTick();
    await wrapper.vm.handleMoveGroups();
    expect(riskMonitor.updateCompany).toHaveBeenCalled();
  });
});
