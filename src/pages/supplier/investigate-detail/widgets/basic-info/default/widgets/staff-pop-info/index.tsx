import { defineComponent, onMounted, ref } from 'vue';
import _ from 'lodash';
import { Popover } from 'ant-design-vue';

import QIcon from '@/components/global/q-icon';
import QTag from '@/components/global/q-tag';

import styles from './staff-pop-info.module.less';
import { numberToHuman } from '@/utils/number-formatter';

const StaffPopInfo = defineComponent({
  name: 'StaffPopInfo',
  props: {
    companyInfo: {
      type: Object,
    },
  },
  setup(props) {
    const staffData = ref<any[]>([]);
    const staffDataColumns = ref<{ title: string }[]>([]);

    const handleStaffData = () => {
      const data = props.companyInfo?.StaffHisCntPopUpInfo || [];
      // 构建表格列配置
      const columns: { title: string }[] = [];

      if (data.length > 0) {
        // 添加数据来源列
        columns.push({ title: '数据来源' });

        // 添加年份列
        const yearList = data[0]?.YearList || [];
        yearList.forEach((yearItem) => {
          columns.push({ title: `${yearItem.Year}年` });
        });
      }
      // 更新数据
      staffData.value = data;
      staffDataColumns.value = columns;

      // 处理来源标签
      _.forEach(staffData.value, (staff) => {
        if (staff.Type === 0) {
          let descMap: string[] = [];
          if (staff.YearList?.length) {
            _.forEach(staff.YearList, (year) => {
              if (year.Data) {
                year.tag = year.SourceDesc.substring(0, 2);
                descMap.push(year.tag);
                year.showEnter = true;
              }
            });
          }
          descMap = Array.from(new Set(descMap));
          if (descMap?.length === 1) {
            staff.tag = descMap[0];
          }
        }
      });
    };

    onMounted(() => {
      handleStaffData();
    });

    return {
      staffData,
      staffDataColumns,
    };
  },
  render() {
    return (
      <Popover trigger="hover" placement="topRight" overlayClassName={styles.staffPopover}>
        <QIcon class={styles.icon} type="icon-a-xianduanxia" />
        <div slot="content">
          <div class={styles.title}>员工人数</div>
          <q-plain-table>
            <tr>
              {this.staffDataColumns.map((item) => (
                <th>{item.title}</th>
              ))}
            </tr>
            {this.staffData.map((item) => (
              <tr>
                <td class={styles.type}>
                  {item.TypeDesc} {item.tag ? <QTag>{item.tag}</QTag> : null}
                </td>
                {item.YearList.map((year) => (
                  <td>{numberToHuman(year.Data)}</td>
                ))}
              </tr>
            ))}
          </q-plain-table>
        </div>
      </Popover>
    );
  },
});

export default StaffPopInfo;
