import { useDimensions } from '@/shared/composables/use-company-store';
import { getSortedTenderSettings } from '@/shared/composables/use-bidding-detail';

import { TenderResultList } from './bidding-investigation-detail.config';

const { tenderTypeMap } = useDimensions();
export const calcWid = (type) => {
  let wid;
  switch (type) {
    case 'PurchaseIllegal':
      wid = 110;
      break;
    case 'risk_inner_blacklist':
      wid = 88;
      break;
    default:
      wid = 100;
  }
  return wid;
};

export const generateColoums = (openStatic) => {
  const content = getSortedTenderSettings(openStatic);
  return content.map((item) => {
    return {
      title: tenderTypeMap.value[item.key],
      width: calcWid(item.key),
      dataIndex: `${item.key}PassData`,
      scopedSlots: {
        customRender: 'dimensionRisk',
      },
    };
  });
};

export const getTableColumns = (openStatic = [] as any[], showDimension = false) => {
  const commonColoums = [
    {
      title: '项目名称',
      width: 220,
      scopedSlots: {
        customRender: 'projectName',
      },
    },
    {
      title: '项目编号',
      dataIndex: 'projectNo',
      width: 160,
    },
    {
      title: '排查编号',
      dataIndex: 'tenderNo',
      width: 180,
    },
    {
      title: '排查主体',
      width: 230,
      scopedSlots: {
        customRender: 'companyList',
      },
    },
    {
      title: '排查结果',
      width: 120,
      key: 'result',
      scopedSlots: {
        customRender: 'advice',
      },
    },
  ];
  return [
    ...commonColoums,
    ...(showDimension
      ? generateColoums(openStatic)
      : [
          {
            title: '风险维度',
            scopedSlots: {
              customRender: 'riskDimension',
            },
          },
        ]),
    {
      title: '排查详情',
      width: 72,
      scopedSlots: {
        customRender: 'action',
      },
      fixed: 'right',
    },
  ];
};

export const FILTER_GROUPS = [
  {
    field: 'filters',
    label: '',
    type: 'groups',
    children: [
      {
        field: 'result',
        label: '排查结果',
        type: 'multiple',
        // layout: 'inline',
        options: [...TenderResultList],
      },
    ],
  },
];

export const BiddingDimensions = [
  {
    key: 'ContactNumber',
    name: '相同电话号码',
  },
  {
    key: 'Mail',
    name: '相同邮箱',
  },
  {
    key: 'Address',
    name: '相同经营地址',
  },
  {
    key: 'Guarantor',
    name: '相互担保关联',
  },
  {
    key: 'ChattelMortgage',
    name: '动产抵押关联',
  },
  {
    key: 'EquityPledge',
    name: '股权出质关联',
  },
  {
    key: 'UpAndDownRelation',
    name: '上下游关联',
  },
  {
    key: 'BidCollusive',
    name: '围串标关联',
  },
  {
    key: 'Website',
    name: '相同域名信息',
  },
  {
    key: 'Patent',
    name: '相同专利信息',
  },
  {
    key: 'IntPatent',
    name: '相同国际专利信息',
  },
  {
    key: 'SoftwareCopyright',
    name: '相同软件著作权',
  },
  {
    key: 'Case',
    name: '相同司法案件',
  },
  {
    key: 'SameNameEmployee',
    name: '疑似同名主要人员',
  },
];

const innerBlackDimension = {
  HitInnerBlackList: '被列入内部黑名单',
  Invest: '对外投资企业被列入内部黑名单',
  Shareholder: '参股股东（企业）被列入内部黑名单',
  Employ: '董监高/法人关联',
  ActualController: '相同实际控制人',
  HisInvest: '历史对外投资',
  HisShareholder: '历史参股股东',
  HisEmploy: '历史董监高/法人关联',
  Branch: '分支机构',
};

const interestConflictDimension = {
  StaffWorkingOutsideForeignInvestment: '潜在利益冲突-投资任职',
  StaffSameName: '疑似潜在利益冲突-疑似同名',
  StaffSamePhone: '疑似潜在利益冲突-相同联系方式',
};

export const dimensionsDictionaryMap = {
  risk_inner_blacklist: innerBlackDimension,
  risk_interest_conflict: interestConflictDimension,
};
