import {
  getLinkDesc,
  getTenderRelationLevelStyle,
  TenderResultList,
  TenderResultMap,
} from '@/shared/config/bidding-investigation-detail.config';
import CompanyStatus from '@/components/global/q-company-status';
import { Group } from '@/components/global/q-filter/interface';
import { DEFAULT_DATE_RANGE } from '@/config/tender.config';
import QTag from '@/components/global/q-tag';
import InterestBatch from '@/assets/images/interest-batch.png';
import BidGuide from '@/assets/images/bid-guide.png';
import { InterestPermissionCode } from '@/shared/constants/diligence.constant';

export const routeMap = (batchId) => {
  return {
    record: [{ title: '历史记录', route: { name: 'interest-investigation-history' } }, { title: '排查结果' }],
    batchRecord: [
      {
        title: '批量特定利益关系排查结果',
        route: { name: 'interest-batch-detail', query: { batchId, useCacheQuery: true } },
      },
      { title: '排查结果' },
    ],
    default: [{ title: '特定利益关系排查', route: { name: 'interest-investigation' } }, { title: '排查结果' }],
  };
};

export const INTEREST_INVESTIGATION_COMPANY_COLUMNS = [
  {
    title: '序号',
    width: 56,
    align: 'center',
    customRender: (text, item) => {
      return {
        children: item.rootIndex + 1,
        attrs: item.attrs,
      };
    },
  },
  {
    title: '企业名称',
    width: 280,
    dataIndex: 'companyName',
    // scopedSlots: { customRender: 'companyName' },
    customRender: (text, item) => {
      return {
        children: (
          <a href={`/embed/companyDetail?keyNo=${item.companyId}&title=${item.companyName}`} target="_blank">
            {text}
          </a>
        ),
        attrs: item.attrs,
      };
    },
  },
  {
    title: '风险维度',
    width: 158,
    customRender: (text, item) => {
      return {
        children: (
          <div>{!item.dimensionName ? <span style={{ color: '#999' }}>被排查企业 未发现 异常风险信息</span> : item.dimensionName}</div>
        ),
        attrs: {
          colSpan: item.level === 0 ? 2 : 1,
        },
      };
    },
  },
  {
    title: '排查明细',
    customRender: (text, item) => {
      const tagData = getTenderRelationLevelStyle(item.hitType?.length ? item.hitType : item.key);
      return {
        children: (
          <div class="items-center flex">
            {tagData.label && <QTag style={tagData.style}>{tagData.label}</QTag>}
            <div domPropsInnerHTML={getLinkDesc(item)}></div>
          </div>
        ),
        attrs: {
          colSpan: item.level === 0 ? 0 : 1,
        },
      };
    },
  },
  {
    title: '排查结果',
    // scopedSlots: { customRender: 'investigationResult' },
    customRender: (item) => {
      return {
        children: <CompanyStatus status={TenderResultMap[item.level]?.label} type={TenderResultMap[item.level]?.type} />,
        attrs: item.attrs,
      };
    },
  },
];

export const DILIGENCE_SEARCH_FILTER_GROUPS: Group[] = [
  {
    field: 'filters',
    label: '筛选条件',
    type: 'groups',
    children: [
      {
        field: 'status',
        type: 'multiple',
        label: '排查结果',
        options: TenderResultList,
        layout: 'inline',
      },
      {
        field: 'operators',
        type: 'multiple',
        label: '操作人',
        options: [],
        layout: 'inline',
        meta: {
          showFilter: true,
        },
      },
      {
        field: 'sd',
        type: 'single',
        label: '排查时间',
        options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
        custom: {
          type: 'date-range',
        },
      },
    ],
  },
];

export const INTEREST_INVESTIGATION_HISTORY_TCS = [
  {
    title: '项目名称',
    width: 250,
    scopedSlots: {
      customRender: 'tenderProjectName',
    },
  },
  {
    title: '项目编号',
    width: 160,
    dataIndex: 'projectNo',
  },
  {
    title: '排查编号',
    width: 140,
    unChangeAble: true,
    dataIndex: 'recordNo',
  },
  {
    title: '排查主体',
    width: 270,
    scopedSlots: {
      customRender: 'companyList',
    },
  },
  {
    title: '排查结果',
    width: 91,
    sorter: true,
    key: 'result',
    scopedSlots: {
      customRender: 'tenderResult',
    },
  },
  {
    title: '命中维度',
    unChangeAble: true,
    width: 440,
    scopedSlots: {
      customRender: 'interestHitDimensions',
    },
  },
  {
    title: '操作人',
    width: 105,
    dataIndex: 'editor.name',
  },
  {
    title: '排查时间',
    width: 150,
    sorter: true,
    key: 'createDate',
    dataIndex: 'createDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    title: '操作',
    width: 80,
    unChangeAble: true,
    fixed: 'right',
    scopedSlots: {
      customRender: 'tenderAction',
    },
  },
];

export const TABS = [
  {
    label: 'Interest Relation Screening',
    value: 'interest-investigation',
    permission: [InterestPermissionCode.single],
  },
  { label: 'Interest Relation Screening Batch', value: 'interest-batch', permission: [InterestPermissionCode.batch] },
  {
    label: 'Interest Relation Screening Records',
    value: 'interest-investigation-history',
    permission: [InterestPermissionCode.history],
  },
];

const desText = [
  '1、特定利益关系排查采用先进的大数据处理技术，挖掘特定利益关系上下游之间的交叉持股、控制关系、相同人员以及相同经营地址、相同联系方式、相同担保关联等关联关系，识别潜在虚假贸易的可能性，协助企业高效防控虚假贸易业务；',
  '2、批量特定利益关系排查，是针对上传项目对应的企业名录进行以下检查:a.上下游为母子公司或由相同的实际控制人控制;b.上下游企业交叉持股;c.上下游企业主要负责人、董事、监事、高级管理人员相同;d.上下游企业注册地址、实际办公地址、业务联系人或联系电话相同;e.上下游企业一方为另一方贸易合同履约提供担保；f.上下游企业存在长期业务关系，一方为另一方的重要供应商或特约经销商；g.其他根据实质重于形式原则认定存在特定利益关系的情形，如股权出质关联、动产抵押关联等；',
  '3、批量特定利益关系排查，最多同时支持<em>1000</em>个项目进行排查，单个项目排查企业数不超过<em>10</em>家。',
];

const singleDesText = [
  '1、特定利益关系排查采用先进的大数据处理技术，挖掘特定利益关系上下游之间的交叉持股、控制关系、相同人员以及相同经营地址、相同联系方式、相同担保关联等关联关系，识别潜在虚假贸易的可能性，协助企业高效防控虚假贸易业务；',
  '2、特定利益关系排查，是针对排查项目做如下排查:a.上下游为母子公司或由相同的实际控制人控制;b.上下游企业交叉持股;c.上下游企业主要负责人、董事、监事、高级管理人员相同;d.上下游企业注册地址、实际办公地址、业务联系人或联系电话相同;e.上下游企业一方为另一方贸易合同履约提供担保；f.上下游企业存在长期业务关系，一方为另一方的重要供应商或特约经销商；g.其他根据实质重于形式原则认定存在特定利益关系的情形，如股权出质关联、动产抵押关联等。',
];

export const SINGLE_GUIDE_DATA = {
  desText: singleDesText,
  img: BidGuide,
};

export const BATCH_GUIDE_DATA = {
  desText,
  img: InterestBatch,
};

export const batchDetailColumns = [
  {
    title: '项目名称',
    width: 280,
    scopedSlots: {
      customRender: 'projectName',
    },
  },
  {
    title: '项目编号',
    width: 200,
    dataIndex: 'projectNo',
  },
  {
    title: '排查编号',
    width: 200,
    dataIndex: 'recordNo',
  },
  {
    title: '排查主体',
    width: 240,
    scopedSlots: {
      customRender: 'companyList',
    },
  },
  {
    title: '排查结果',
    width: 100,
    key: 'result',
    scopedSlots: {
      customRender: 'advice',
    },
  },
  {
    title: '命中维度',
    scopedSlots: {
      customRender: 'interestHitDimensions',
    },
  },
  {
    title: '排查详情',
    width: 72,
    scopedSlots: {
      customRender: 'action',
    },
    fixed: 'right',
  },
];

export const FILTER_GROUPS = [
  {
    field: 'filters',
    label: '',
    type: 'groups',
    children: [
      {
        field: 'result',
        label: '排查结果',
        type: 'multiple',
        // layout: 'inline',
        options: [...TenderResultList],
      },
    ],
  },
];

enum InterestInvestigationStatus {
  processing = 0,
  success = 1,
  failed = 2,
}

export const INTEREST_INVESTIGATION_PAGE = {
  [InterestInvestigationStatus.processing]: 'interest-investigation-processing',
  [InterestInvestigationStatus.success]: 'interest-investigation-detail',
  [InterestInvestigationStatus.failed]: 'interest-investigation-failed',
};

export const EXTERNAL_INTEREST_INVESTIGATION_PAGE = {
  [InterestInvestigationStatus.processing]: 'external-interest-investigation-processing',
  [InterestInvestigationStatus.success]: 'external-interest-investigation-detail',
  [InterestInvestigationStatus.failed]: 'external-interest-investigation-failed',
};
