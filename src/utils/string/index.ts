import { replace } from 'lodash';
import { isValidEmail, isValidPhone } from '@/utils/validator';

export const replaceEnter2Br = (value) => replace(value, /\n/g, '<br />');

export const replaceWhitespace2Nbsp = (value) => replace(value, / /g, '&nbsp;');

export const formatChar2HtmlEntity = (value) => replaceEnter2Br(replaceWhitespace2Nbsp(value));

export const limitLength =
  (len = 32) =>
  (str = '') => {
    const safetyStr = String(str);

    if (safetyStr.length > len) {
      return `${safetyStr.slice(0, len)}...`;
    }
    return safetyStr;
  };

const desensitizeEmail = (str: string) => {
  const [prefix, suffix] = str.split('@');
  if (prefix.length > 4) {
    return `${prefix.slice(0, 3)}****${prefix.slice(-1)}@${suffix}`;
  }
  return `${prefix.slice(0, 1)}****@${suffix}`;
};

export const desensitization = (str) => {
  if (!str) return str;
  if (isValidEmail(str)) {
    return desensitizeEmail(str);
  }
  if (isValidPhone(str)) {
    return `${str.slice(0, 3)}****${str.slice(-4)}`;
  }
  return str;
};
