import Vue, { computed, defineComponent, nextTick, onMounted, provide, ref, unref } from 'vue';
import { Button, Col, Row, Skeleton, Spin, Tooltip, message } from 'ant-design-vue';
import { chunk, cloneDeep } from 'lodash';
import { useMediaQuery } from '@vueuse/core';
import moment from 'moment';
import { register } from 'swiper/element/bundle';
import ECharts from 'vue-echarts';

import HeroicLayout from '@/shared/layouts/heroic';
import { formatNumber } from '@/utils/format';
import { customer } from '@/shared/services';
import { useI18n } from '@/shared/composables/use-i18n';
import RiskAction from '@/shared/components/risk-action';
import AreaData from '@/shared/constants/area.constant';
import Empty from '@/shared/components/empty';
import { useDOMScreenshot } from '@/hooks/use-dom-screenshot';
import Icon from '@/shared/components/icon';
import QIcon from '@/components/global/q-icon';
import { MapColor } from '@/config';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import QSelect from '@/components/global/q-select';
import QCard from '@/components/global/q-card';
import { useUserStore } from '@/shared/composables/use-user-store';
import NoAnnualReview from '@/shared/components/no-anni-review';
import { getAnniHistoryHook, useRiskStore } from '@/shared/composables/use-analysis-dashboard';

import RiskScoreBlock from './widgets/risk-score-block';
import RiskChartBlock from './widgets/risk-chart-block';
import { LeftTriangleIcon, RightTriangleIcon } from './widgets/icon';
import { useSwiper } from './hooks/use-swiper';
import RiskHistorySelector from './widgets/risk-history-selector';
import { openRiskCompanyDrawer } from './widgets/risk-company-drawer';
import { DefaultOpSettings, useSelectOptionRelatedHook } from './hooks/use-select-option';
import styles from './analysis-dashboard.page.module.less';

const getRegionNameByCode = (code: string) => {
  return AreaData.find((item) => item.value === code)?.label;
};

const getRegionCodeByName = (name: string) => {
  return AreaData.find((item) => item.label === name)?.value;
};

// FIXME: 移到 onMounted 执行
register();

const AnalysisDashboardPage = defineComponent({
  name: 'AnalysisDashboardPage',
  setup() {
    const { locale } = useI18n();
    const initFlag = ref(false);
    const noAnniData = ref(false); // 无巡检数据
    const loading = ref(true);

    const { profile } = useUserStore();
    const isEnglish = computed(() => locale.value === 'en');
    const swiperRef = ref();
    // 页面进入参数重置
    const { data, params, initValue } = useRiskStore();
    params.value = cloneDeep(initValue);

    const lastUpdate = ref('');
    // 是否大屏
    const isLargeScreen = useMediaQuery('(min-width:1440px)');

    const { getBatchHistory, historyData } = getAnniHistoryHook();

    const registerMap = async () => {
      return new Promise((resolve) => {
        import('@/utils/echarts').then(({ default: install }) => {
          install(Vue);
          resolve(true);
        });
      });
    };

    const Option = ref({ departments: [] as any[], principals: [] as any[], groups: [] as any[], labels: [] as any[] });

    const analysisOpSettings = [
      DefaultOpSettings[0],
      {
        staticKey: 'principals',
        dataKey: 'customerPrincipals',
        valKey: 'value',
        paramKey: 'principals',
      },
      ...DefaultOpSettings.slice(1),
    ];
    const { paramsChange, updateParamsChange, updateOptions, lastClick } = useSelectOptionRelatedHook(
      [0, 0, 0, 0],
      analysisOpSettings,
      Option
    );
    const swiperChunk = computed(() => chunk(data.value?.deps ?? [], 6));

    const { handleSwiperPrev, handleSwiperNext, hasNext, hasPrev, currentIndex } = useSwiper(
      swiperRef,
      computed(() => swiperChunk.value.length)
    );

    // 要比对的历史记录（当前选中）
    const compareHistoryId = ref<undefined | number>();

    const fetchData = async (param?) => {
      let res;
      try {
        loading.value = true;
        // risk/v2在只有一次排查的时候，不支持-1传参，要赋值成batchIdCurrent
        const query = cloneDeep(param || params.value);
        if (query.batchIdPrevious === -1) {
          query.batchIdPrevious = query.batchIdCurrent;
        }
        res = await customer.getRiskCharts(query);
        if (!param) {
          data.value = res;
        }
      } catch (error) {
        console.error(error);
      } finally {
        loading.value = false;
      }
      return res;
    };

    const refresh = async () => {
      try {
        loading.value = true;
        data.value = await customer.updateRiskCharts({
          orgId: profile.value.currentOrg,
          batchId: params.value.batchIdCurrent,
        });
      } finally {
        loading.value = false;
      }
    };

    // 执行比对

    const init = async () => {
      try {
        loading.value = true;
        const res = await getBatchHistory();
        noAnniData.value = !res?.data?.length;
        if (res?.data?.length >= 1) {
          params.value.batchIdCurrent = res.data[0].batchId;
          lastUpdate.value = moment(res.data[0].createDate).format('YYYY-MM-DD HH:mm');
          params.value.batchIdPrevious = res.data?.[1]?.batchId || res.data[0].batchId;
          await registerMap();
          await fetchData();
          await nextTick();
        }
      } finally {
        loading.value = false;
      }
    };

    const areaChartOptions = computed(() => {
      return {
        visualMap: {
          type: 'continuous',
          // show: false,
          min: 0,
          max: 200,
          inRange: {
            color: unref(MapColor),
          },
          itemHeight: 100,
          itemWidth: 18,
          // itemGap: 5,
          text: ['>200', '0'],
          textGap: 3,
          textStyle: {
            color: '#666',
          },
        },
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
        },
        series: [
          {
            name: '客户数量',
            data: (data.value?.regions ?? []).map((item) => {
              return {
                name: getRegionNameByCode(item.code),
                value: item.count,
                selected: item.code === params.value.province?.[0],
              };
            }),
            type: 'map',
            map: 'china',
            selectedMode: 'single',
            zoom: 1.25,
            center: [104, 34],
            emphasis: {
              label: {
                color: '#666',
              },
              // itemStyle: {
              //   areaColor: '#128bed',
              // },
            },
            itemStyle: {
              areaColor: '#E1F2F9',
              borderWidth: '0.2',
              // borderColor: '#fff',
            },
          },
        ],
      };
    });

    const areaCountList = computed(() => [
      {
        title: 'Total Third Party',
        count: data.value?.summary?.totalCount || 0,
        unit: isEnglish.value ? '' : '家',
        params: {
          result: undefined,
        },
      },
      {
        title: 'High Risk Third Party',
        count: data.value?.summary?.[2] || 0,
        unit: isEnglish.value ? '' : '家',
        params: {
          result: [2],
        },
      },
      {
        title: 'Risk Third Party',
        count: data.value?.summary?.riskCount || 0,
        unit: isEnglish.value ? '' : '家',
        params: {
          result: [1, 2],
        },
      },
      {
        title: 'Medium Risk Third Party',
        count: data.value?.summary?.[1] || 0,
        unit: isEnglish.value ? '' : '家',
        params: {
          result: [1],
        },
      },
      {
        title: 'Risk Third Party Percents',
        count: `${(data.value?.summary?.riskPercent * 100).toFixed(2)}`,
        unit: '%',
        unitStyle: { fontSize: 'inherit' },
      },
      {
        title: 'Low Risk Third Party',
        count: data.value?.summary?.[0],
        unit: isEnglish.value ? '' : '家',
        params: {
          result: [0],
        },
      },
    ]);

    const track = useTrack();

    const selectValueChange = async (name, index, length) => {
      track(createTrackEvent(6242, '分析看板', name));
      updateParamsChange(index, length);
      const opdata = await fetchData(); // 正常的筛选结果
      if (!length) {
        const { dataKey, paramKey } = analysisOpSettings[lastClick.value];
        const query = Object.assign(cloneDeep(params.value), { [paramKey]: [] });
        const res2 = await fetchData(query);
        opdata.batchAggs[dataKey] = res2.batchAggs[dataKey];
      }
      updateOptions(opdata);
    };

    provide('groups', Option);
    onMounted(async () => {
      initFlag.value = true;
      await init();
      updateOptions(data.value);
      initFlag.value = false;
      track(createTrackEvent(6240, '分析看板'));
    });

    const [dashboardRef, downloadScreenshot] = useDOMScreenshot();
    const [mapRef, downloadMapScreenshot] = useDOMScreenshot();
    const [statRef, downloadStatScreenshot] = useDOMScreenshot();

    return {
      loading,
      initFlag,
      noAnniData,
      formatNumber,
      areaChartOptions,
      areaCountList,
      data,
      isLargeScreen,
      fetchData,
      lastUpdate,
      handleSwiperPrev,
      handleSwiperNext,
      swiperRef,
      swiperChunk,
      hasNext,
      hasPrev,
      refresh,
      currentIndex,

      downloadScreenshot,
      dashboardRef,
      mapRef,
      downloadMapScreenshot,
      statRef,
      downloadStatScreenshot,

      compareHistoryId,
      Option,
      params,
      historyData,
      paramsChange,
      updateParamsChange,
      updateOptions,
      selectValueChange,
    };
  },
  render() {
    const { tc, locale } = useI18n();

    if (this.noAnniData) {
      return (
        <HeroicLayout init={this.initFlag}>
          <NoAnnualReview />
        </HeroicLayout>
      );
    }

    return (
      <HeroicLayout loading={this.loading && this.initFlag}>
        <QCard
          rootStyle={{ minHeight: 'calc(100vh - 72px)', display: 'flex', flexDirection: 'column' }}
          bodyStyle={{
            display: 'flex',
            backgroundColor: 'inherit',
            flex: '1',
            padding: '0',
          }}
        >
          <div class={[styles.ricon, 'flex']} slot="title">
            <span>{tc('Dashboard')}</span>
            <RiskAction
              v-permission={[2072]}
              icon="icon-a-shuaxinquanbuyidu1x"
              loading={this.loading}
              onClick={async () => {
                this.$track(createTrackEvent(6242, '分析看板', '刷新统计'));
                await this.refresh();
                message.success(tc('Refresh Success'));
                this.paramsChange = [0, 0, 0, 0];
                Object.assign(this.params, {
                  departments: [] as any[],
                  principals: [] as any[],
                  groupIds: [] as any[],
                  labelIds: [] as any[],
                  province: [] as any[],
                });
                const res = await this.fetchData();
                this.updateOptions(res);
              }}
              spin
              theme="slight"
              data-testid="refresh"
            >
              <span class={styles.refreshText}>刷新统计</span>
            </RiskAction>
          </div>
          <div slot="extra" class={styles.extra}>
            <div style="color: #666">最新巡检时间: {this.lastUpdate}</div>
            <RiskHistorySelector
              historyData={this.historyData}
              currentAnalysisId={this?.data?.latestAnalyzedId}
              value={this.params.batchIdPrevious}
              onChange={(id) => {
                this.$track(createTrackEvent(6242, '分析看板', '对比时间'));
                this.params.batchIdPrevious = id;
                Object.assign(this.params, {
                  departments: [] as any[],
                  groupIds: [] as any[],
                  labelIds: [] as any[],
                  province: [] as any[],
                });
                this.paramsChange = [0, 0, 0, 0];
                this.fetchData();
              }}
            />
            <QSelect
              multiple
              placeholder="所属部门"
              options={[...this.Option.departments]}
              v-model={this.params.departments}
              onChange={() => {
                this.selectValueChange('所属部门', 0, this.params.departments?.length);
              }}
            />
            <QSelect
              multiple
              placeholder="负责人"
              options={[...this.Option.principals]}
              v-model={this.params.principals}
              onChange={() => {
                this.selectValueChange('负责人', 1, this.params.principals?.length);
              }}
            />
            <QSelect
              multiple
              placeholder="分组"
              options={[...this.Option.groups]}
              v-model={this.params.groupIds}
              onChange={() => {
                this.selectValueChange('分组', 2, this.params.groupIds?.length);
              }}
            />
            <QSelect
              placeholder="标签"
              multiple
              options={[...this.Option.labels]}
              v-model={this.params.labelIds}
              onChange={() => {
                this.selectValueChange('标签', 3, this.params.labelIds?.length);
              }}
              showFilter={true}
            />

            <Button
              type="primary"
              v-debounceclick={() => {
                this.downloadScreenshot();
                this.$track(createTrackEvent(6242, '分析看板', '导出'));
              }}
            >
              <QIcon type="icon-xiazai" />
              {tc('export')}
            </Button>
          </div>

          {this.loading ? (
            <Spin class={styles.spin} spinning />
          ) : (
            <div
              ref="dashboardRef"
              style={{
                display: 'flex',
                flex: 1,
                overflow: 'hidden',
              }}
            >
              <div class={styles.overview}>
                {/* 概览 */}
                {!this.loading ? (
                  <div style={{ flex: '1', minHeight: '280px', minWidth: '260px', position: 'relative' }} ref="mapRef">
                    <div class={styles.title}>
                      <span>
                        {tc('Overview')}
                        <Tooltip overlayClassName={styles.tooltip} placement={'bottomLeft'}>
                          <div slot="title" style={{ fontSize: '14px', lineHeight: '22px' }}>
                            {tc('Heat Map Desc')}
                          </div>
                          <QIcon
                            style={{
                              marginLeft: '5px',
                              cursor: 'pointer',
                              color: '#d8d8d8',
                            }}
                            type="icon-zhushi"
                          />
                        </Tooltip>
                      </span>
                      <div
                        data-capture="exclude"
                        class={styles.download}
                        style={{ top: '2px' }}
                        v-debounceclick={() => {
                          this.$track(createTrackEvent(6903, '分析看板', '下载', '概览'));
                          this.downloadMapScreenshot();
                        }}
                      >
                        <Icon type="icon-xiazai" />
                      </div>
                    </div>
                    <ECharts
                      option={this.areaChartOptions}
                      class={styles.chart}
                      autoresize
                      onMapselectchanged={async (val) => {
                        this.$track(createTrackEvent(6903, '分析看板', '地图', '概览'));

                        const [{ name }] = val.batch;
                        const code = getRegionCodeByName(name);
                        const selected = this.data?.regions.find((item) => item.code === code);
                        // 没有数据的不执行

                        if (this.params.province && code === this.params.province[0]) {
                          this.params.province = undefined;
                        } else {
                          this.params.province = [code];
                        }

                        if (!Number(selected?.count)) {
                          this.params.province = undefined;
                          return;
                        }
                        await nextTick();
                        this.fetchData();
                      }}
                      ref="chartMapRef"
                    />
                    {/* <div style={{ position: 'absolute', left: '10px', bottom: '10px' }}>
                      <VisualMap color={this.areaChartOptions.visualMap.inRange.color}></VisualMap>
                    </div> */}
                  </div>
                ) : null}
                {/* 风险统计 */}
                <div class={styles.overviewList}>
                  <div class={styles.header}>
                    <div class={styles.title}>
                      <span>
                        {tc('Risk Statistics')}
                        <q-glossary-info tooltip="风险巡检列表企业风险概况统计"></q-glossary-info>
                      </span>
                    </div>
                    {!this.loading ? (
                      <div
                        class={styles.download}
                        data-capture="exclude"
                        onClick={() => {
                          this.$track(createTrackEvent(6903, '分析看板', '下载', '风险统计'));
                          this.downloadStatScreenshot();
                        }}
                      >
                        <Icon type="icon-xiazai" />
                      </div>
                    ) : null}
                  </div>

                  {!this.loading ? (
                    <div style={{ display: 'flex', flexWrap: 'wrap', margin: '-5px -15px -5px' }} ref="statRef">
                      {this.areaCountList?.map((item) => (
                        <div
                          key={item.title}
                          class={styles.overviewItem}
                          style={{
                            flex: 1,
                            minWidth: '40%',
                          }}
                        >
                          <div
                            class={{ [styles.itemValue]: true, [styles.disabled]: !item.params || !item.count }}
                            onClick={() => {
                              this.$track(createTrackEvent(6903, '分析看板', tc(item.title), '风险统计'));

                              if (!item.params || !item.count) return;
                              const { params } = useRiskStore();

                              openRiskCompanyDrawer({ ...unref(params), ...item.params, title: tc(item.title) });
                            }}
                            data-testid={tc(item.title)}
                          >
                            <span>{Number(item.count || 0).toLocaleString('en-US')}</span>
                            {locale.value !== 'en' ? <span class={styles.unit}>{item.unit}</span> : null}
                          </div>
                          <span
                            class={[styles.itemLabel]}
                            style={{
                              lineHeight: locale.value === 'en' ? '18px' : '22px',
                              minHeight: locale.value === 'en' ? '36px' : 'auto',
                            }}
                          >
                            {tc(item.title)}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : null}
                </div>
              </div>
              <div style={{ height: 'inherit', width: '1px', backgroundColor: '#eee', margin: '15px 0' }}></div>
              <div class={styles.main}>
                {/* 维度分析 */}
                <div class={styles.mainInfo}>
                  <RiskScoreBlock value={this?.data?.types} />
                </div>
                {/* 部门统计 */}
                {this.swiperChunk?.length && !this.loading ? (
                  <div>
                    <div class="flex items-center justify-between" style={{ marginBottom: '10px' }}>
                      <div>
                        <span style={{ fontSize: '14px', lineHeight: '22px', fontWeight: 'bold' }}>{tc('Departmental Statistics')}</span>
                        <q-glossary-info tooltip="按部门对风险巡检列表企业风险等级占比进行统计。"></q-glossary-info>
                        <span class={styles.departmentInfo}>
                          共计{this.data.summary.totalCount}家企业，其中{this.data.mulitDepCount}家被多个部门管理
                          {this.data.mulitDepCount > 0 && (
                            <span>
                              ，
                              <a
                                onClick={(e) => {
                                  e.stopPropagation();
                                  const { params } = useRiskStore();

                                  openRiskCompanyDrawer({
                                    ...unref(params),
                                    title: '合计',
                                    isMulitDep: 1,
                                    batchIdPrevious: -1,
                                    result: [],
                                  });
                                }}
                              >
                                点击查看
                              </a>
                            </span>
                          )}
                        </span>
                      </div>
                      <div
                        class={styles.swiperPatinationWrapper}
                        style={{ visibility: this.swiperChunk?.length > 1 ? 'visible' : 'hidden' }}
                      >
                        <Button disabled={!this.hasPrev} onClick={this.handleSwiperPrev} size="small">
                          <i class="anticon">
                            <LeftTriangleIcon />
                          </i>
                        </Button>
                        <Button disabled={!this.hasNext} onClick={this.handleSwiperNext} size="small">
                          <i class="anticon">
                            <RightTriangleIcon />
                          </i>
                        </Button>
                      </div>
                    </div>

                    <swiper-container
                      allow-touch-move="false"
                      ref="swiperRef"
                      space-between={15}
                      mousewheel={false}
                      pagination={false}
                      {...{
                        on: {
                          slidechange: (e) => {
                            const activeIndex = e.target.swiper?.activeIndex;
                            this.currentIndex = activeIndex || 0;
                          },
                        },
                      }}
                    >
                      {this.swiperChunk?.map((chunkItem, indexKey) => (
                        <swiper-slide key={indexKey}>
                          <Row type="flex" class={styles.mainChart} gutter={[10, 10]}>
                            {chunkItem?.map((item, index) => {
                              return (
                                <Col key={index} span={8}>
                                  <Skeleton loading={this.loading} active>
                                    <RiskChartBlock item={item} is-large-screen={this.isLargeScreen} />
                                  </Skeleton>
                                </Col>
                              );
                            })}
                          </Row>
                        </swiper-slide>
                      ))}
                    </swiper-container>
                  </div>
                ) : null}

                {!this.swiperChunk?.length && !this.loading ? (
                  <div style={{ flex: '1', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Empty type="search" description="暂无数据" />
                  </div>
                ) : null}
              </div>
            </div>
          )}
        </QCard>
      </HeroicLayout>
    );
  },
});

export default AnalysisDashboardPage;
