import { defineComponent, onMounted, ref } from 'vue';
import { <PERSON>, Col, Spin, Drawer, But<PERSON> } from 'ant-design-vue';

import { createPromiseDialog } from '@/components/promise-dialogs';
import { customer as customerService } from '@/shared/services';
import NATIONAL_INDUSTRY_OPTIONS from '@/shared/constants/national-industry.constant';
import { statusCodeList } from '@/components/global/q-company-status/config';
import { COMPANY_SCALE_C, DEFAULT_DATE_RANGE_LIFE, REGISTERED_CAPITAL_RANGE, treasuryTypeList } from '@/config/tender.config';
import QIcon from '@/components/global/q-icon';
import { useDOMScreenshot } from '@/hooks/use-dom-screenshot';
import { useI18n } from '@/shared/composables/use-i18n';

import QBar from './q-bar';
import QPie from './q-pie';
import QMap from './q-map';
import ChartCard from './chart-card';
import styles from './analysis-chart-modal.module.less';
import SwitchButton from '@/shared/components/switch-button';
import { openEnlargeChartModal } from './enlarge-chart-modal';

const flattenTreeToPaths = (nodes) => {
  const result: any[] = [];

  function traverse(node, path) {
    // 将当前路径推入结果
    const newPath = [...path, node.value];
    result.push(newPath);

    // 如果有子节点，则继续递归
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => traverse(child, newPath));
    }
  }

  // 开始遍历树的每个根节点
  nodes.forEach((node) => traverse(node, []));
  return result;
};
const chartList = [
  {
    title: '国标行业分布',
    key: 'industry',
    field: 'i',
    chartType: 'pie',
    dict: NATIONAL_INDUSTRY_OPTIONS,
    parse: (data) => flattenTreeToPaths([data]),
    type: 'multiple',
    sortable: true,
  },
  {
    title: '企业规模分析',
    key: 'scale',
    chartType: 'pie',
    dict: COMPANY_SCALE_C,
    type: 'multiple',
    sortable: true,
  },
  {
    title: '登记状态分析',
    key: 'statusCode',
    chartType: 'pie',
    dict: statusCodeList,
    type: 'multiple',
    sortable: true,
  },
  {
    title: '企业性质分析',
    key: 'treasuryType',
    chartType: 'pie',
    dict: treasuryTypeList,
    type: 'multiple',
    sortable: true,
  },
  {
    title: '注册资本分析',
    key: 'registcapiAmount',
    chartType: 'pie',
    dict: REGISTERED_CAPITAL_RANGE,
    type: 'multiple',
  },
  {
    title: '成立年限分析',
    key: 'startDateCode',
    chartType: 'pie',
    dict: DEFAULT_DATE_RANGE_LIFE,
    type: 'single',
  },
  {
    title: '企查分分析',
    key: 'creditRate',
    chartType: 'pie',
    type: 'single',
    disableSearch: true,
  },
  {
    title: '实缴资本分析',
    key: 'reccapamount',
    chartType: 'pie',
    dict: REGISTERED_CAPITAL_RANGE,
    type: 'multiple',
  },
  {
    title: '员工人数分析',
    key: 'employeecount',
    chartType: 'pie',
    type: 'single',
    disableSearch: true,
  },
  {
    title: '参保人数分析',
    key: 'insuredCount',
    chartType: 'pie',
    type: 'single',
    disableSearch: true,
  },
  {
    title: '营业收入分析',
    key: 'companyRevenue',
    chartType: 'pie',
    type: 'single',
    disableSearch: true,
  },
];

const pieList = chartList.filter((v) => v.chartType === 'pie');

const filterAction = [
  {
    label: '分组',
    value: 'group',
    color: '#5B8FF9',
  },
  {
    label: '标签',
    value: 'label',
    color: '#61DDAA',
  },
  {
    label: '部门',
    value: 'department',
    color: '#65789B',
  },
];

const AnalysisChartModal = defineComponent({
  name: 'AnalysisChartModal',
  setup(props, { emit }) {
    const visible = ref(true);
    const activeTab = ref(filterAction[0].value);
    const activeColor = ref(filterAction[0].color);
    const loading = ref(false);

    const fullData = ref<Record<string, any>>({});

    const getChartData = async () => {
      try {
        loading.value = true;
        const res = await customerService.chart();
        fullData.value = res;
        loading.value = false;
      } catch (error) {
        console.error(error);
        loading.value = false;
      }
    };

    const handleCancel = () => {
      visible.value = false;
    };

    const goSearch = (data, config) => {
      let value = data.value;
      if (config.type === 'multiple') {
        value = [data.value];
      }
      if (config.parse) {
        value = config.parse(data);
      }
      const filter = { [config.field || config.key]: value };
      emit('resolve', { filter, config });
    };

    onMounted(() => {
      getChartData();
    });

    const { tc } = useI18n();
    const [dashboardRef, downloadScreenshot] = useDOMScreenshot();

    const enlargePie = async (config) => {
      const data = fullData.value[config.key];
      const clickedData = (await openEnlargeChartModal({
        ...config,
        data,
        totalCount: fullData.value.totalCount,
      })) as { data: any; config: any } | undefined;
      if (clickedData) {
        goSearch(clickedData, config);
      }
    };

    return {
      loading,
      fullData,
      activeTab,
      activeColor,
      visible,
      handleCancel,
      goSearch,
      enlargePie,

      tc,
      dashboardRef,
      downloadScreenshot,
    };
  },
  render() {
    return (
      <Drawer
        width="1200px"
        visible={this.visible}
        afterVisibleChange={this.handleCancel}
        onClose={this.handleCancel}
        headerStyle={{
          padding: '11px 0',
        }}
      >
        <div slot="title" class={styles.modalTitle}>
          <div>{this.tc('Third Party Analysis')}</div>
          <div v-show={!this.loading}>
            <Button
              type="primary"
              v-debounceclick={() => {
                this.downloadScreenshot();
              }}
            >
              <QIcon type="icon-xiazai" />
              {this.tc('export')}
            </Button>
          </div>
        </div>
        {this.loading ? (
          <Spin>
            <div style={{ height: '600px' }}></div>
          </Spin>
        ) : (
          <div style={{ padding: '15px' }} ref="dashboardRef">
            <Row gutter={[10, 10]}>
              <Col span={12}>
                <ChartCard title="地区分布">
                  <QMap
                    dataSource={this.fullData.region}
                    onAction={(params) => {
                      const config = {
                        type: 'multiple',
                        field: 'r',
                        parse: (data) => flattenTreeToPaths([data]),
                      };
                      this.goSearch(params, config);
                    }}
                  />
                </ChartCard>
              </Col>
              <Col span={12}>
                <ChartCard title="特征分析">
                  <div slot="action">
                    <SwitchButton
                      value={this.activeTab}
                      options={filterAction}
                      onChange={(mode, index) => {
                        this.activeTab = mode;
                        this.activeColor = filterAction[index].color;
                      }}
                    />
                  </div>
                  <QBar
                    currentTab={this.activeTab}
                    activeColor={this.activeColor}
                    dataSource={this.fullData[this.activeTab]}
                    onAction={(params) => {
                      const configs = {
                        group: {
                          type: 'multiple',
                          field: 'g',
                        },
                        label: {
                          type: 'multiple',
                          field: 't',
                        },
                        department: {
                          type: 'multiple',
                          field: 'departmentIds',
                        },
                      };
                      this.goSearch(params, configs[this.activeTab]);
                    }}
                  />
                </ChartCard>
              </Col>
              {pieList.map((item) => {
                return (
                  <Col span={8}>
                    <ChartCard title={item.title}>
                      <div slot="action" class={styles.btnEnlarge} data-capture="exclude" onClick={() => this.enlargePie(item)}>
                        <QIcon type="icon-quanping1" />
                      </div>
                      <QPie
                        type={item.key}
                        dataSource={this.fullData[item.key]}
                        dict={item.dict}
                        config={item}
                        onAction={this.goSearch}
                        total={this.fullData.totalCount}
                      />
                    </ChartCard>
                  </Col>
                );
              })}
            </Row>
          </div>
        )}
      </Drawer>
    );
  },
});

export default AnalysisChartModal;

export const openAnalysisChartModal = createPromiseDialog(AnalysisChartModal);
