import { getCurrentInstance } from 'vue';

import { getDynamicRiskDimensionURL, noDynamicRiskDimensionDetail, useDynamicRiskDimensionDetail } from '..';

describe('noDynamicRiskDimensionDetail', () => {
  test('should return true for keys that are in DYNAMIC_RISK_DIMENSION_NO_DETAIL', () => {
    const sourceKeys = [
      'MonitorSeriousIllegality', // 严重违法
      'MonitorBusinessAbnormal', // 经营异常
      'MonitorAbnormalHousehold', // 经营异常
      'MonitorBusinessStatus', // 被列入非正常户
      'MonitorCertificationExpired', // 资质证书过期提醒
    ];
    sourceKeys.forEach((key) => {
      expect(noDynamicRiskDimensionDetail(key)).toBe(true);
    });
  });

  test('should return false for keys that are not in DYNAMIC_RISK_DIMENSION_NO_DETAIL', () => {
    const sourceKeys = ['key1', 'key2', 'key3'];
    sourceKeys.forEach((key) => {
      expect(noDynamicRiskDimensionDetail(key)).toBe(false);
    });
  });
});

describe('getDynamicRiskDimensionURL', () => {
  test('should return the correct URL for AdministrativePenalties', () => {
    const key = 'AdministrativePenalties';
    const item = { id: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/adminpenaltydetail?id=123';
    expect(getDynamicRiskDimensionURL(key, item)).toBe(expectedURL);
  });

  test('should return the correct URL for FinancialRegulation', () => {
    const key = 'FinancialRegulation';
    const item = { id: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/adminpenaltydetail?id=123';
    expect(getDynamicRiskDimensionURL(key, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorRestrictedConsumption', () => {
    const key = 'MonitorRestrictedConsumption';
    const item = { id: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/sumptuary?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(key, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorInquiryEvaluation', () => {
    const key = 'MonitorInquiryEvaluation';
    const item = { id: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/inquiryEvaluation?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(key, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorPublicSecurityNotice', () => {
    const key = 'MonitorPublicSecurityNotice';
    const item = { KeyNo: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/news-detail-page?keyNo=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(key, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorJudicialAuction', () => {
    const key = 'MonitorJudicialAuction';
    const item = { id: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/judicialSale?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(key, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorProductRecall', () => {
    const key = 'MonitorProductRecall';
    const item = { id: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/recall-product?url=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(key, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorAssetsAuction', () => {
    const key = 'MonitorAssetsAuction';
    const item = { id: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/assetsaleDetail?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(key, item)).toBe(expectedURL);
  });

  test('should return the correct URL for MonitorBillDispute', () => {
    const key = 'MonitorBillDispute';
    const item = { id: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/judgementInfo?id=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(key, item)).toBe(expectedURL);
  });

  test('should return the correct URL for unknown', () => {
    const key = 'UNKNOWN';
    const item = { CaseSearchId: '123', companyName: 'Test Company' };
    const expectedURL = '/embed/courtCaseDetail?caseId=123&title=Test Company';
    expect(getDynamicRiskDimensionURL(key, item)).toBe(expectedURL);
  });
});

// Mock getCurrentInstance
vi.mock('vue', () => ({
  getCurrentInstance: vi.fn(),
}));

describe('useDynamicRiskDimensionDetail', () => {
  test('should open a new window with the correct URL when record.dimensionKey is not support', () => {
    const mockWindowOpen = vi.fn();
    Object.defineProperty(window, 'open', {
      value: mockWindowOpen,
      writable: false,
    });

    // Arrange
    const record = {
      dimensionKey: 'TestDimension',
      objectId: '123',
      companyName: 'Test Company',
    };
    const expectedURL = '/embed/courtCaseDetail?caseId=123&title=Test Company';
    // Act
    const [openDimensionDetail] = useDynamicRiskDimensionDetail();
    openDimensionDetail(record);
    // Assert
    expect(mockWindowOpen).toHaveBeenCalledWith(expectedURL);
  });

  test.each([
    [
      'MonitorEquityPledge',
      // Arrange
      {
        dimensionKey: 'MonitorEquityPledge',
        objectId: 'OBJECT_ID',
      },
      // Assert
      'pledge',
      {
        KeyNo: 'COMPANY_ID',
        pledgeId: 'OBJECT_ID',
      },
    ],
    [
      'MonitorGuaranteeInfo',
      // Arrange
      {
        dimensionKey: 'MonitorGuaranteeInfo',
        dynamicId: 'DYNAMIC_ID',
      },
      // Asert
      'riskGuarantor',
      {
        KeyNo: 'COMPANY_ID',
        riskId: 'DYNAMIC_ID',
      },
    ],
  ])(
    'should call $modal.showDimension with the correct parameters when record.dimensionKey is support - %s',
    (dimensionKey, record, ...expectedParams) => {
      const mockShowDimension = vi.fn();
      vi.mocked<any>(getCurrentInstance).mockReturnValue({
        proxy: {
          $modal: {
            showDimension: mockShowDimension,
          },
        },
      });
      // Arrange
      const source = {
        companyId: 'COMPANY_ID',
        companyName: 'Test Company',
        ...record,
      };

      const [openDimensionDetail] = useDynamicRiskDimensionDetail();
      openDimensionDetail(source);
      // Assert
      expect(mockShowDimension).toHaveBeenCalledWith(...expectedParams);
    }
  );
});
