import { ref, set, unref } from 'vue';
import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash';

import { useMonitorSettingStore } from '@/shared/composables/use-monitor-setting-store';
import { useSettingStore } from '@/hooks/use-setting-store';

export const useSettingModalLogic = (riskFieldsMap, { props, emit }, pageFrom = 'diligence') => {
  const useStore = pageFrom === 'diligence' ? useSettingStore : useMonitorSettingStore;
  const { flatSettings, getAnotherOpenSetting } = useStore();
  const data = ref(cloneDeep(props.params.data));

  const visible = ref(true);

  const updateItem = (item, val) => {
    item.fieldVal = val;
  };
  const getError = (item) => {
    const fieldItem = riskFieldsMap[item.field];
    if (!fieldItem?.validator) {
      return false;
    }
    return fieldItem.validator(item.fieldVal);
  };

  const handleSubmit = async () => {
    // 判断校验是否通过，不通过就返回，不继续执行
    const currentSelectItem = unref(data);

    const hasError = currentSelectItem.strategyModel?.detailsParams
      ?.map((item) => {
        return getError(item);
      })
      .filter(Boolean)?.length;

    if (hasError) {
      return Promise.reject();
    }

    const params = {
      status: props.params.status,
      riskKey: props.params.riskKey,
      items: [
        {
          status: currentSelectItem.status,
          sort: currentSelectItem.sort,
          key: currentSelectItem.key,
          strategyModel: {
            ...currentSelectItem.strategyModel,
            detailsParams: currentSelectItem.strategyModel?.detailsParams
              ? currentSelectItem.strategyModel?.detailsParams.map((v) => {
                  return {
                    field: v.field,
                    fieldOperator: v.fieldOperator,
                    fieldVal: v.fieldVal,
                  };
                })
              : undefined,
          },
          type: currentSelectItem.type,
        },
      ],
    };

    // 是否是当前维度最后一个开启的设置
    const isLastOpen = !unref(flatSettings).find(
      (item) => item.status && item.parentKey === props.params.riskKey && item.key !== currentSelectItem.key
    );

    // 如果是最后一个开关，关闭父节点
    if (isLastOpen) {
      params.status = currentSelectItem.status;
      set(props.params, 'status', currentSelectItem.status);
    }

    emit('resolve', data.value);

    return Promise.resolve(true);
  };

  const changeSetting = (item) => {
    if (!getAnotherOpenSetting(item.key)?.length && item.status === 1) {
      message.warning(props.params?.warningMsg || '请至少开启一项风险排查事项设置');
      return;
    }
    data.value = item;
    item.status = item.status === 0 ? 1 : 0;
  };

  return {
    visible,
    data,
    updateItem,
    changeSetting,
    getError,
    handleSubmit,
  };
};
