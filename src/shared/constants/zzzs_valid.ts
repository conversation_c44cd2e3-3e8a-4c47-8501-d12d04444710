import { flattenDeep } from 'lodash';

export const zzzsList = [
  {
    label: '建筑业资质',
    value: 'A001',
    list: [
      {
        label: '工程设计资质证书',
        value: '004001',
      },
      {
        label: '工程勘察资质证书',
        value: '004002',
      },
      {
        label: '工程设计与施工资质证书',
        value: '004003',
      },
      {
        label: '建筑业企业资质证书',
        value: '004004',
        list: [
          {
            label: '专业承包',
            value: '004004002',
          },
          {
            label: '施工总承包',
            value: '004004001',
          },
        ],
      },
      {
        label: '工程监理资质证书',
        value: '004005',
      },
      {
        label: '工程造价咨询企业资质证书',
        value: '004007',
      },
      {
        label: '建设工程质量检测机构资质证书',
        value: '004008',
      },
      {
        label: '对外承包工程资格证书',
        value: '902',
      },
      {
        label: '进省备案登记凭证',
        value: '004009',
      },
      {
        label: '公路工程监理资质证书',
        value: '054001',
      },
      {
        label: '施工劳务资质',
        value: '088001',
      },
    ],
  },
  {
    label: '房地产业资质',
    value: 'A002',
    list: [
      {
        label: '房地产开发企业资质证书',
        value: '007',
      },
      {
        label: '房地产估价机构资质证书',
        value: '009',
      },
      {
        label: '物业服务企业资质证书',
        value: '903',
      },
    ],
  },
  {
    label: '水利、环境和公共设施管理业资质',
    value: 'A003',
    list: [
      {
        label: '水利安全生产标准化证书',
        value: '006001',
      },
      {
        label: '排污许可证',
        value: '027001',
      },
      {
        label: '排污登记凭证',
        value: '027002',
      },
      {
        label: '危险废物经营许可证',
        value: '070',
      },
    ],
  },
  {
    label: '科学研究和技术服务业资质',
    value: 'A004',
    list: [
      {
        label: '检验检测机构资质认定证书',
        value: '003',
      },
      {
        label: '建设工程规划许可证',
        value: '004010',
      },
      {
        label: '地质灾害防治单位资质证书',
        value: '010',
      },
      {
        label: '工程咨询单位备案',
        value: '042001',
      },
      {
        label: '安全评价机构资质证书',
        value: '043001',
      },
      {
        label: '安全生产检测检验机构资质证书',
        value: '043002',
      },
      {
        label: '测绘资质证书',
        value: '052',
      },
    ],
  },
  {
    label: '信息传输、软件和信息技术服务业资质',
    value: 'A005',
    list: [
      {
        label: '电信业务经营许可证',
        value: '001',
      },
      {
        label: 'CMMI证书',
        value: '018',
      },
      {
        label: '软件企业证书',
        value: '019001',
      },
      {
        label: '软件产品证书',
        value: '019002',
      },
      {
        label: '信息系统服务交付能力认证证书',
        value: '032',
      },
      {
        label: '电信设备进网许可证',
        value: '034001',
      },
      {
        label: '电子认证服务许可',
        value: '036',
      },
      {
        label: '电信网码号使用许可',
        value: '904',
      },
      {
        label: '通信建设企业资质证书',
        value: '905',
      },
    ],
  },
  {
    label: '电力、热力、燃气及水生产和供应业资质',
    value: 'A007',
    list: [
      {
        label: '电力业务许可证',
        value: '008002',
      },
      {
        label: '燃气经营许可证',
        value: '093001',
      },
    ],
  },
  {
    label: '制造业资质',
    value: 'A008',
    list: [
      {
        label: '危险化学品经营许可证',
        value: '057001',
      },
      {
        label: '危险化学品登记证',
        value: '057002',
      },
      {
        label: '安全生产许可证',
        value: '011',
      },
      {
        label: '工业产品生产许可证',
        value: '074',
      },
      {
        label: '成品油零售许可证',
        value: '096001',
      },
      {
        label: '饲料和饲料添加剂生产许可证',
        value: '075001',
      },
      {
        label: '特种设备安装、改造、修理许可证',
        value: '015001003',
      },
    ],
  },
  {
    label: '药械妆资质（药品、医疗器械和化妆品资质）',
    value: 'A015',
    list: [
      {
        label: '医疗器械生产许可证',
        value: '012001001',
      },
      {
        label: '医疗器械生产备案凭证',
        value: '012001002',
      },
      {
        label: '医疗器械经营许可证',
        value: '012002001',
      },
      {
        label: '医疗器械经营备案凭证',
        value: '012002002',
      },
      {
        label: '医疗器械注册证',
        value: '012003001',
      },
      {
        label: '医疗器械备案凭证',
        value: '012003002',
      },
      {
        label: '药品生产许可证',
        value: '013001',
      },
      {
        label: '药品经营许可证',
        value: '013002',
      },
    ],
  },
  {
    label: '交通运输、仓储和邮政业资质',
    value: 'A012',
    list: [
      {
        label: '道路运输经营许可证',
        value: '089001',
      },
    ],
  },
  {
    label: '文化、体育和娱乐业资质',
    value: 'A013',
    list: [
      {
        label: '印刷经营许可证',
        value: '065001',
      },
    ],
  },
  {
    label: '食品类资质',
    value: 'A014',
    list: [
      {
        label: '食品生产许可证',
        value: '023001',
      },
      {
        label: '食品经营许可证',
        value: '023002',
      },
      {
        label: '食品经营备案凭证',
        value: '023003',
      },
    ],
  },
  {
    label: '管理体系认证',
    value: 'A017',
    list: [
      {
        label: '质量管理体系认证',
        value: '000002001',
        list: [
          { label: '质量管理体系认证(ISO9001)', value: '002004001012' },
          { label: '建设施工行业质量管理体系认证', value: '002004001002' },
          { label: '汽车行业质量管理体系认证', value: '002004001003' },
          { label: '航空业质量管理体系认证', value: '002004001004' },
          { label: '航空器维修质量管理体系认证', value: '002004001005' },
          { label: '航空仓储销售商质量管理体系认证', value: '002004001006' },
          { label: '电讯业质量管理体系认证', value: '002004001007' },
          { label: '医疗器械质量管理体系认证', value: '002004001008' },
          { label: '德国汽车工业协会质量管理体系认证', value: '002004001009' },
          { label: '电气与电子元件和产品有害物质过程控制管理体系认证', value: '002004001010' },
          { label: '国际铁路行业质量管理体系认证', value: '002004001011' },
        ],
      },
      {
        label: '环境管理体系认证',
        value: '000002002',
      },
      {
        label: '职业健康安全管理体系认证',
        value: '000002003',
      },
      {
        label: '食品农产品管理体系认证',
        value: '000002004',
      },
      {
        label: '信息安全管理体系认证',
        value: '000002005',
      },
    ],
  },
  {
    label: '服务认证',
    value: '000003',
  },
  {
    label: '自愿性产品认证',
    value: '000004',
    list: [
      {
        label: '国家信息安全产品认证',
        value: '002002001004',
      },
      {
        label: '数据安全管理认证',
        value: '002002001007',
      },
      {
        label: 'CE认证',
        value: '002002001028',
      },
    ],
  },
  {
    label: '食品农产品认证',
    value: '000005',
  },
  {
    label: '其他资质',
    value: 'A999',
    list: [
      {
        label: 'CCC工厂认证',
        value: '900',
      },
      {
        label: '医疗机构执业许可证',
        value: '095001',
      },
    ],
  },
];

export const zzzsTotalList = flattenDeep(
  zzzsList.map((item: any) => {
    return [item, ...(item?.list || []), ...(item?.list?.map((el: any) => el?.list || []) || [])];
  })
);

// 子维度值顺序表
export const zzzsSubValueList = zzzsList.map((item) => {
  if (item.list) {
    return item.list.map((sub) => sub.value);
  }
  return [item.value];
});

// 递归函数实现
function flattenData(data, parentValue) {
  let result: any[] = [];

  data.forEach((item) => {
    if (item.list && Array.isArray(item.list)) {
      result = result.concat(flattenData(item.list, item.value)); // 递归处理子项
    } else {
      result.push({ label: item.label, value: item.value, parentValue });
    }
  });

  return result;
}
// 子维度打平后的数据
export const flattenZzzsList = flattenData(zzzsList, undefined);

export const zzzsMap = flattenZzzsList.reduce((acc, cur) => {
  if (!acc) {
    acc = {};
  }
  acc = acc ?? {};
  acc[cur.value] = cur;
  return acc;
}, {});
