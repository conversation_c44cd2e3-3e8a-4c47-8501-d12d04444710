import { computed, defineComponent, onMounted } from 'vue';
import { Breadcrumb } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router/composables';
import moment from 'moment';

import HeroicLayout from '@/shared/layouts/heroic';
import FullWatermark from '@/components/full-watermark';
import { company as companyService, potential as potentialService } from '@/shared/services';
import { useRequest } from '@/shared/composables/use-request';
import QCard from '@/components/global/q-card';
import QIcon from '@/components/global/q-icon';
import { potentialResultMap } from '@/shared/config/potential-investigation.config';
import Empty from '@/shared/components/empty';

import InvestigationAdvice from './widgets/investigation-advice';
import RiskContent from './widgets/risk-content';
import styles from './potential-investigation-detail.module.less';

const PotentialInvestigationDetail = defineComponent({
  name: 'PotentialInvestigationDetail',
  setup() {
    const route = useRoute();
    const router = useRouter();
    const keyNo = computed(() => route.params.id);

    const getCompanyInfo = async () => {
      if (!route.query?.name) {
        const { Name } = await companyService.getDetail({ keyNo: keyNo.value });
        return Name;
      }
      return route.query.name;
    };

    const getRisk = async (forceRescan = false) => {
      if (route.query.id && !forceRescan) {
        const res = await potentialService.detail(route.query.id);
        return res;
      }
      const companyName = await getCompanyInfo();
      const res = await potentialService.scanRisk({
        companyId: keyNo.value,
        companyName,
      });
      if (res.id) {
        await router.replace({
          ...route,
          query: {
            ...route.query,
            id: res.id,
          },
        });
      }
      return res;
    };

    const { isLoading, data, execute } = useRequest(getRisk);

    const dataSource = computed<any>(() => data.value || {});

    const hasDetail = computed(() => {
      const dimensions = dataSource.value.details?.groupResults || {};
      return Object.entries(dimensions).some(([key, value]: [string, any]) => value?.length > 0);
    });

    const handleRescan = async () => {
      execute(true);
    };

    onMounted(async () => {
      await execute();
    });

    return {
      keyNo,
      isLoading,
      dataSource,
      handleRescan,
      hasDetail,
    };
  },
  render() {
    let result = '未发现利冲风险';
    let riskLevel = 0;
    const hitDimensions = Object.keys(this.dataSource.details?.groupResults ?? {}) || [];
    if (hitDimensions.includes('StaffWorkingOutsideForeignInvestment')) {
      riskLevel = 2;
      result = '潜在利益冲突';
    } else if (hitDimensions.length === 1 && hitDimensions.includes('SuspectedInterestConflict')) {
      riskLevel = 1;
      result = '疑似潜在利益冲突';
    }

    return (
      <div style={{ paddingTop: '40px' }}>
        <Breadcrumb class="breadcrumb">
          <Breadcrumb.Item v-show={this.$route.params.type === 'history'}>
            <a
              onClick={() =>
                this.$router.replace({
                  name: 'potential-investigation-history',
                  query: {
                    useCacheQuery: 'true',
                  },
                })
              }
            >
              <QIcon type="icon-mianbaoxiefanhui" />
              历史记录
            </a>
          </Breadcrumb.Item>
          <Breadcrumb.Item v-show={this.$route.params.type.startsWith('search')}>
            <a
              onClick={() =>
                this.$router.replace({
                  name: 'potential-investigation',
                })
              }
            >
              <QIcon type="icon-mianbaoxiefanhui" />
              潜在利益冲突排查
            </a>
          </Breadcrumb.Item>
          <Breadcrumb.Item v-show={this.$route.params.type === 'batch-detail'}>
            <a
              onClick={() =>
                this.$router.replace({
                  name: 'potential-batch-detail',
                  query: {
                    batchId: this.$route.query.batchId,
                    useCacheQuery: 'true',
                  },
                })
              }
            >
              <QIcon type="icon-mianbaoxiefanhui" />
              批量潜在利益冲突排查详情
            </a>
          </Breadcrumb.Item>
          <Breadcrumb.Item v-show={this.$route.params.type === 'search-list'}>
            <a
              onClick={() =>
                this.$router.replace({
                  name: 'potential-investigation-search',
                  query: {
                    keyword: this.$route.query.keyword,
                  },
                })
              }
            >
              搜索结果
            </a>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{this.dataSource.name}</Breadcrumb.Item>
        </Breadcrumb>

        <FullWatermark
          height={window.innerHeight - 102}
          offset={{
            x: 190,
            y: 102,
          }}
          fillOffset={{
            x: 300,
            y: 300,
          }}
        >
          <HeroicLayout
            loading={this.isLoading}
            innerStyle={{
              minHeight: 'calc(100vh - 112px)',
            }}
          >
            <div class={styles.hero} slot={'hero'}>
              <div class={styles.title}>{this.dataSource.name}</div>
              <div class={styles.operators}>
                <div>
                  <span>操作人：</span>
                  <span>{this.dataSource.editor?.name || '-'}</span>
                </div>
                <div>
                  <span>操作时间：</span>
                  <span>{moment(this.dataSource.createDate).format('YYYY-MM-DD HH:mm:ss') || '-'}</span>
                </div>
              </div>
              <InvestigationAdvice description={this.dataSource.description} result={result} theme={potentialResultMap[riskLevel]?.type} />
            </div>
            <QCard title="排查结果明细" bodyStyle={{ padding: '0' }}>
              {this.hasDetail ? (
                <RiskContent
                  companyInfo={{ keyNo: this.keyNo, name: this.dataSource.name }}
                  details={this.dataSource.details.groupResults}
                  id={this.dataSource.id}
                  onRescan={this.handleRescan}
                />
              ) : (
                <div class={styles.empty}>
                  <Empty type="search" description="未发现被排查企业与内部人员存在潜在利益冲突风险" />
                </div>
              )}
            </QCard>
          </HeroicLayout>
        </FullWatermark>
      </div>
    );
  },
});

export default PotentialInvestigationDetail;
