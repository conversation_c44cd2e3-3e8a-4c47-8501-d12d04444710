import { calcWid } from '@/shared/config/risk-assessment-detail.page.config';

export const TenderDimension = [
  {
    label: '资质筛查',
    key: 'BiddingCompanyCertification',
  },
  {
    label: '深度关系排查',
    key: 'BiddingCompanyRelation',
  },
  {
    label: '涉采购不良行为',
    key: 'PurchaseIllegal',
  },
  {
    label: '共同投标分析',
    key: 'JointBiddingAnalysis',
  },
  {
    label: '内部黑名单',
    key: 'risk_inner_blacklist',
  },
  {
    label: '潜在利益冲突',
    key: 'risk_interest_conflict',
  },
];

export const detailColoumns = () => [
  {
    title: '项目名称',
    show: true,
    width: 200,
    scopedSlots: {
      customRender: 'tenderProjectName',
    },
    fixed: 'left',
    unChangeAble: true,
    onlyShow: true,
  },
  {
    title: '项目编号',
    width: 140,
    show: true,
    dataIndex: 'projectNo',
    fixed: 'left',
    unChangeAble: true,
    onlyShow: true,
  },
  {
    title: '排查主体',
    show: true,
    width: 200,
    scopedSlots: {
      customRender: 'companyList',
    },
    fixed: 'left',
    unChangeAble: true,
    onlyShow: true,
  },
  {
    title: '排查编号',
    width: 140,
    show: true,
    unChangeAble: true,
    dataIndex: 'tenderNo',
  },
  {
    title: '排查结果',
    width: 125,
    sorter: true,
    show: true,
    key: 'result',
    scopedSlots: {
      customRender: 'tenderResult',
    },
  },
  ...TenderDimension.map((item) => {
    const { key, label } = item;
    return {
      title: label,
      width: calcWid(key),
      key,
      show: true,
      unChangeAble: true,
      scopedSlots: {
        customRender: 'tenderDimension',
      },
    };
  }),
  {
    title: '操作人',
    width: 105,
    show: true,
    dataIndex: 'editor.name',
  },
  {
    title: '排查时间',
    width: 150,
    sorter: true,
    show: true,
    key: 'createDate',
    dataIndex: 'createDate',
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    title: '操作',
    width: 80,
    show: true,
    unChangeAble: true,
    fixed: 'right',
    scopedSlots: {
      customRender: 'tenderAction',
    },
  },
];
