import { noop, startsWith, toNumber } from 'lodash';
import { message, Tooltip } from 'ant-design-vue';
import { computed, defineComponent, onBeforeUnmount, provide, ref, shallowReactive, unref } from 'vue';
import { useRouter } from 'vue-router/composables';
import { Route } from 'vue-router';
import { useToggle } from '@vueuse/core';
import moment from 'moment';
import axios from 'axios';

import { company as companyService, diligence as diligenceService } from '@/shared/services';
import HeroicLayout from '@/shared/layouts/heroic';
import RiskTab from '@/pages/supplier/investigate-detail/widgets/risk-tabs';
import RiskContentDefault from '@/pages/supplier/investigate-detail/widgets/risk-content/default';
import RiskAction from '@/shared/components/risk-action';
import { openAuditFollowDrawer } from '@/pages/supplier/investigate-detail/widgets/audit-followup';
import RiskWatch from '@/pages/supplier/investigate-detail/widgets/risk-watch';
import { uesInvestStore } from '@/hooks/use-invest-store';
import { useAbility } from '@/libs/plugins/user-ability';
import Empty from '@/shared/components/empty';
import { ERROR_MESSAGES } from '@/config/message.config';
import { useI18n } from '@/shared/composables/use-i18n';
import { createTrackEvent } from '@/config/tracking-events';
import { GenerateReportStatus, useGenerateReportFile } from '@/shared/composables/use-generate-report-file';
import GenerateReportAction from '@/components/generate-report-action';
import { BatchBusinessTypeEnum } from '@/shared/constants/batch-business-type.constant';

import styles from '@/pages/supplier/investigate-detail/default/investigate-detail.page.module.less';

function getBodyStyle({ paddingTop }) {
  const defaultStyle = {
    background: 'none',
    display: 'flex',
    flexDirection: 'column',
    paddingTop,
  };
  return defaultStyle;
}

const ExternalInvestigateDetailPage = defineComponent({
  name: 'ExternalInvestigateDetailPage',
  props: {
    isExternal: {
      type: Boolean,
      default: false,
    },
  },
  provide() {
    return {
      getCompanyDetail: () => this.company,
    };
  },
  setup() {
    const loading = ref(false);
    const {
      riskInfo,
      companyInfo: company,
      riskLevel,
      snapshotId,
      dimensionDetails,
      qualifications,
      credits,
      updateActiveTab,
      updateExpandKeys,
      updateBasicData,
      getCompanyInfo,
      getQualificationData,
      getDiligenceData,
    } = uesInvestStore();
    const router = useRouter();
    const route = router.currentRoute;

    const snapshotDate = computed(() =>
      unref(riskInfo)?.createDate ? moment(unref(riskInfo)?.createDate).format('YYYY-MM-DD HH:mm:ss') : ''
    );
    // 最新的diligenceId
    const cacheDiligenceId = ref(unref(riskInfo).id || route.query?.diligenceId);
    // 错误处理
    const errorOutOfLimitation = shallowReactive<{ error: boolean; message: string | undefined }>({
      error: false,
      message: undefined,
    });

    /**
     * 获取风险排查详情
     * @param keyNo company keyNo
     * @param needRefreshSnapshot 是否刷新快照：刷新快照时不传 diligenceId 和 snapshotId
     * @param isDynamicDetails 是否是排查详情
     */
    const fetchData = async (keyNo: string, needRefreshSnapshot = false, isDynamicDetails = false) => {
      try {
        loading.value = true;
        errorOutOfLimitation.error = false;
        errorOutOfLimitation.message = undefined;
        updateExpandKeys([]);
        // 请求不同公司时，清除缓存数据
        if (unref(company).KeyNo && unref(company).KeyNo !== keyNo) {
          updateBasicData();
          cacheDiligenceId.value = undefined;
        }
        await getCompanyInfo(keyNo);
        updateActiveTab('all');
        if (!company.value?.Name) {
          return;
        }

        // 通过 URL query 指定模型设置
        let settingId = toNumber(route.query?.settingId);
        settingId = settingId > 0 ? settingId : undefined; // 规避 settingId 为空的情况

        await getDiligenceData({
          keyNo,
          diligenceId: needRefreshSnapshot ? undefined : unref(cacheDiligenceId),
          isDynamicDetails,
          // 刷新快照时不传 `settingId`
          settingId: needRefreshSnapshot ? undefined : settingId,
          // 刷新快照时需要传 `orgSettingsId`
          ambiguousSettingId: needRefreshSnapshot ? riskInfo.value.orgSettingsId : undefined,
        });
        cacheDiligenceId.value = unref(riskInfo).id;
        // 仅在重新生成快照时更新 URL
        if (needRefreshSnapshot || unref(riskInfo).notMatch) {
          await router
            .replace({
              query: {
                ...router.currentRoute.query, // 获取实时 query
                diligenceId: unref(riskInfo).id,
              },
            })
            .catch(noop);
        }
        // 获取资质信息
        await getQualificationData(unref(company).KeyNo);
      } catch (error) {
        if (axios.isAxiosError(error)) {
          if (error.response?.data.code === 400203 && error.response?.data.statusCode === 403) {
            errorOutOfLimitation.error = true;
            errorOutOfLimitation.message = ERROR_MESSAGES.OUT_OF_DILIGENCE_LIMITATION;
          }
        } else {
          console.error(error);
        }
      } finally {
        loading.value = false;
      }
    };

    onBeforeUnmount(() => {
      updateBasicData();
      cacheDiligenceId.value = '';
    });

    // 生成快照
    const [refreshingSnapshot, setRefreshingSnapshot] = useToggle(false);

    /** 轮询报告生成状态 */
    const [genReportState, genReportPolling, resetGenReportState] = useGenerateReportFile(BatchBusinessTypeEnum.Diligence_Report_Export);

    const ability = useAbility();
    const refreshSnapshot = async () => {
      const hasAbility = await ability.check('stock', ['DiligenceHistoryQuantity', 'DiligenceDailyQuantity']);
      if (refreshingSnapshot.value || !hasAbility) {
        return;
      }
      setRefreshingSnapshot(true);
      const params = {
        companyId: company.value.KeyNo,
        companyName: company.value.Name,
      };
      try {
        // 刷新快照
        await diligenceService.refreshSnapshot(params);
        // 更新排查数据
        await fetchData(company.value.KeyNo, true);
        resetGenReportState();
      } catch (error) {
        console.error(error);
      } finally {
        setRefreshingSnapshot(false);
      }

      genReportState.status = GenerateReportStatus.Idle; // 重置生成状态
    };

    const getCompanyTabs = (passedRoute: Route) => {
      const ids = ((passedRoute.query.ids as string) || '').split(',');
      const names = ((passedRoute.query.names as string) || '').split(',');
      return ids.map((id: string, i: number) => {
        return {
          id,
          name: names[i],
        };
      });
    };

    /**
     * FIXME: 临时解决方案，`dynamicDetails` 路由会被 `investigation-trends` 替代
     */
    const isInvestigationTrends = (type?: string) => {
      if (!type) {
        return false;
      }
      return ['risk-trends', 'dynamicDetails'].includes(type);
    };

    const handleRouteChange = (params, oldParams: any = {}) => {
      if (params.id !== oldParams.id) {
        fetchData(params.id, false, isInvestigationTrends(params?.type));
      }
    };

    /** 生成报告 */
    const handleGenerateReport = async () => {
      if (!(await ability.check('stock', ['DiligenceReportQuantity']))) {
        return;
      }
      if (!riskInfo.value?.id) {
        message.warning('数据异常，请刷新重试');
        return;
      }
      try {
        genReportState.status = GenerateReportStatus.Pending;
        const { batchId, businessType } = await diligenceService.pollingGenerateReport(riskInfo.value?.id);
        message.success('您的报告正在生成中，成功后我们将第一时间提醒您');

        // 嵌入页面支持监听 batchJob
        window.parent.postMessage(
          {
            type: 'createBatchJob',
            data: {
              batchId,
              businessType,
            },
          },
          '*'
        );
        genReportPolling(batchId);
      } catch (error) {
        genReportState.status = GenerateReportStatus.Idle;
        const errorMessage = error?.response?.data?.error ?? '报告生成失败，请稍后再试';
        message.error(errorMessage);
      }
    };

    /**
     * 注入
     */
    provide('snapshotId', snapshotId);

    return {
      dimensionDetails,
      riskInfo,
      company,
      loading,
      riskLevel,
      handleRouteChange,
      getCompanyTabs,

      refreshSnapshot,
      refreshingSnapshot,
      snapshotDate,
      handleGenerateReport,
      fetchData,
      errorOutOfLimitation,
      isInvestigationTrends,

      genReportState,
      qualifications,
      credits,
    };
  },
  watch: {
    '$route.params': {
      handler: 'handleRouteChange',
      immediate: true,
      deep: true,
    },
  },
  render() {
    const { company, loading, riskLevel, $route } = this;
    const { riskInfo } = uesInvestStore();
    /**
     * 批量排查（5家以下）时，显示多家公司的 tab
     */
    const tabs = this.getCompanyTabs($route).map((tab) => ({
      ...tab,
      isActive: tab.id === $route.params.id,
    }));

    const riskContentConfig = {
      props: {
        loading,
        dimensions: this.dimensionDetails,
        company,
        tabs,
        riskLevel,
        qualifications: this.qualifications,
        credits: this.credits,
        offset: {
          x: 0,
          y: 0,
        },
        breadcrumbHeight: 0,
        isExternal: this.isExternal,
      },
    } as any;
    const bodyStyle = getBodyStyle({ paddingTop: '' });
    const { tc } = useI18n();
    return (
      <div class={[styles.container, { [styles.watermark]: riskInfo.value.snapshotId && !loading }]}>
        <HeroicLayout bodyStyle={bodyStyle} loading={loading}>
          {!loading && company?.KeyNo ? (
            <div>
              <RiskTab tabs={tabs} />
              <RiskContentDefault {...riskContentConfig}>
                {riskContentConfig.props.dimensions.length > 0 && !this.isInvestigationTrends($route.params?.type) ? (
                  <template slot="extra">
                    <Tooltip title="点击将刷新页面重新排查">
                      <RiskAction
                        icon="icon-icon_sqq"
                        loading={this.refreshingSnapshot}
                        v-debounceclick={this.refreshSnapshot}
                        theme="slight"
                      >
                        {tc('Screening time')}: {this.snapshotDate}
                      </RiskAction>
                    </Tooltip>

                    <GenerateReportAction
                      genReportState={this.genReportState}
                      onGenerateReport={this.handleGenerateReport}
                      permission={[2002]}
                    />

                    <RiskAction
                      v-permission={[2003]}
                      icon="icon-a-lianji4"
                      onClick={async () => {
                        this.$track(createTrackEvent(6204, '准入排查详情页', '审核跟进'));
                        const needRefresh = await openAuditFollowDrawer({
                          ...riskContentConfig.props,
                          id: this.riskInfo?.id,
                        });
                        if (needRefresh) {
                          await this.fetchData(this.company.KeyNo, false);
                        }
                      }}
                    >
                      {tc('Follow-up')}
                    </RiskAction>
                    <RiskWatch v-show={!this.isExternal} v-permission={[2102]} />
                  </template>
                ) : null}
              </RiskContentDefault>
            </div>
          ) : null}
          {/* 用量超限 */}
          <div class={styles.limitation} v-show={this.errorOutOfLimitation.error}>
            <Empty type="search" description={this.errorOutOfLimitation.message} />
          </div>
        </HeroicLayout>
      </div>
    );
  },
});

export default ExternalInvestigateDetailPage;
