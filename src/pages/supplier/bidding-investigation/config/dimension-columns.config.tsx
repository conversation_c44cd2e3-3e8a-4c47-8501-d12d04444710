import { Tooltip } from 'ant-design-vue';
import moment from 'moment';
import { uniq } from 'lodash';

import QEntityLink from '@/components/global/q-entity-link';
import QTag from '@/components/global/q-tag';
import { blacklistDurationFormatter } from '@/shared/config/investigate-detail-risk-columns.config';

import { openBidInfoDrawer } from '../../../../shared/composables/open-dialog-drawer';
import QIcon from '@/components/global/q-icon';

const SourceTypes = {
  1: '行政处罚',
  2: '裁判文书',
  3: '不良行为',
  4: '招投标负面信息-公共资源不良行为',
  5: '外部黑名单',
};

export const AdministrativePenalties2Columns = [
  {
    title: '决定文书号',
    dataIndex: 'caseno',
  },
  {
    title: '企业名称',
    customRender: (text, record) => {
      return (
        <QEntityLink
          ellipsis={false}
          coyObj={{
            Name: record.name,
            KeyNo: record.keyno ?? record.KeyNo,
          }}
        ></QEntityLink>
      );
    },
  },
  {
    title: '类型',
    width: 60,
    dataIndex: 'punishreasontype',
    scopedSlots: { customRender: 'punishReason' },
  },
  {
    title: '违法事实',
    width: 200,
    dataIndex: 'casereason',
    scopedSlots: { customRender: 'shrinkContent' },
  },
  {
    title: '处罚结果',
    dataIndex: 'title',
    placeholder: '未公示',
    scopedSlots: { customRender: 'shrinkContent' },
  },
  {
    title: '处罚金额(元)',
    width: 100,
    dataIndex: 'penalsum',
    sorter: true,
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '处罚单位',
    width: 150,
    dataIndex: 'court',
  },
  {
    title: '处罚日期',
    width: 100,
    dataIndex: 'punishdate',
    sorter: true,
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'publishdate',
    sorter: true,
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '原文',
    width: 68,
    scopedSlots: { customRender: 'originalSource' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

export const GovernmentPurchaseIllegalColumns = [
  {
    title: '企业名称',
    width: 260,
    customRender: (text, record) => {
      return (
        <QEntityLink
          ellipsis={false}
          coyObj={{
            Name: record.Name,
            KeyNo: record.KeyNo,
          }}
        />
      );
    },
  },
  {
    title: '黑名单类型',
    dataIndex: 'listtype',
  },
  {
    title: '列入原因',
    dataIndex: 'CaseReason',
    width: 400,
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '列入机关',
    dataIndex: 'Court',
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'decisiondate',
    sorter: true,
    scopedSlots: { customRender: 'date' },
  },
];

export const GovProcurementIllegalColumns = [
  {
    title: '企业名称',
    width: 240,
    customRender: (text, record) => {
      return (
        <QEntityLink
          ellipsis={false}
          coyObj={{
            Name: record.Name,
            KeyNo: record.KeyNo,
          }}
        ></QEntityLink>
      );
    },
  },
  {
    title: '黑名单类型',
    dataIndex: 'listtype',
  },
  {
    title: '黑名单领域',
    dataIndex: 'field',
  },
  {
    title: '列入原因',
    dataIndex: 'CaseReason',
    width: 220,
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '列入机关',
    width: 155,
    dataIndex: 'Court',
  },
  {
    title: '列入日期',
    width: 95,
    dataIndex: 'decisiondate',
    sorter: true,
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '详情',
    width: 60,
    scopedSlots: { customRender: 'govProcurementIllegalDetail' },
  },
];

// 军队采购失信名单
export const ArmyProcurementIllegalColumns = [
  {
    title: '企业名称',
    dataIndex: 'NameAndKeyNo',
    scopedSlots: { customRender: 'qEntityLink' },
  },
  {
    title: '类型',
    dataIndex: 'dimensionName',
  },
  {
    title: '黑名单领域',
    dataIndex: 'field',
  },
  {
    title: '列入原因',
    dataIndex: 'CaseReason',
    width: 280,
  },
  {
    title: '列入机关',
    dataIndex: 'Court',
  },
  // {
  //   title: '列入日期',
  //   width: 103,
  //   dataIndex: 'Publishdate',
  //   scopedSlots: { customRender: 'date' },
  //   sorter: true,
  // },
  {
    title: '列入日期',
    width: 103,
    sorter: true,
    dataIndex: 'decisiondate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'openBDetail' },
  },
];

// 相同司法案件
export const CaseColumns = [
  {
    title: '案件名称',
    dataIndex: 'CaseName',
    width: 260,
    customRender: (text, record) => {
      return (
        <a href={`/embed/courtCaseDetail?caseId=${record.Id}&title=${record.CaseName}`} target="_blank">
          {record.CaseName}
        </a>
      );
    },
  },
  {
    title: '案件类型',
    width: 80,
    dataIndex: 'CaseTypeMain',
  },
  {
    title: '当事人',
    scopedSlots: { customRender: 'RoleAmt' },
  },
  {
    title: '案由',
    width: 100,
    dataIndex: 'CaseReason',
  },
  {
    title: '案号',
    width: 160,
    scopedSlots: { customRender: 'AnNoList' },
  },

  {
    title: '法院',
    width: 160,
    scopedSlots: { customRender: 'CourtList' },
  },
  {
    title: '最新审理程序',
    width: 100,
    customRender: (item) => {
      return (
        <div>
          <div>{item.LastestDate ? moment(item.LastestDate * 1000).format('YYYY-MM-DD') : '-'}</div>
          <div>{item.LatestTrialRound}</div>
        </div>
      );
    },
  },
];

// 相同国际专利信息
export const IntPatentColumns = [
  {
    title: '发明名称',
    dataIndex: 'Title',
    width: 292,
    customRender: (text, record) => {
      return (
        <a href={`/embed/intPatentDetail?id=${record.Id}&title=${record.Title}`} target="_blank">
          {record.Title}
        </a>
      );
    },
  },

  {
    title: '申请号',
    width: 180,
    dataIndex: 'ApplicationNumber',
  },
  {
    title: '申请日期',
    width: 98,
    dataIndex: 'ApplicationDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '公开(公告)号',
    width: 140,
    dataIndex: 'PublicationNumber',
  },
  {
    title: '公开(公告)日期',
    width: 107,
    dataIndex: 'PublicationDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '发明人',
    customRender: (_text, record) => {
      const text = record.Inventors?.join('、');
      return <Tooltip title={text}>{text}</Tooltip>;
    },

    ellipsis: true,
  },
];

// 相同专利信息
export const PatentColumns = [
  {
    title: '发明名称',
    width: 200,
    dataIndex: 'Title',
    customRender: (text, record) => {
      return (
        <a href={`/embed/patentDetail?id=${record.Id}&title=${record.Title}`} target="_blank">
          {record.Title}
        </a>
      );
    },
  },
  {
    title: '申请人',
    dataIndex: 'NameAndKeyno',
    customRender: (data) => {
      return <QEntityLink ellipsis={false} coyArr={data}></QEntityLink>;
    },
  },
  {
    title: '专利类型',
    dataIndex: 'KindCodeDesc',
    width: 90,
  },
  {
    title: '法律状态',
    width: 145,
    dataIndex: 'LegalStatusDesc',
    customRender: (text, record) => {
      const TypeMap = {
        权利恢复: 'success',
        部分无效: 'success',
        授权: 'success',
        公开: 'primary',
        公布: 'primary',
        实质审查: 'primary',
        权利终止: 'danger',
        公布视为撤回: 'danger',
        公布驳回: 'danger',
        避重放弃: 'danger',
      };
      return <QTag type={TypeMap[record.LegalStatusDesc] ?? 'danger'}>{record.LegalStatusDesc}</QTag>;
    },
  },
  {
    title: '申请号',
    width: 140,
    dataIndex: 'ApplicationNumber',
  },
  {
    title: '申请日期',
    width: 98,
    dataIndex: 'ApplicationDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '公开(公告)号',
    width: 120,
    dataIndex: 'PublicationNumber',
  },
  {
    title: '公开(公告)日期',
    width: 107,
    dataIndex: 'PublicationDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  // {
  //   title: '发明人',
  //   dataIndex: 'InventorList',
  //   width: 130,
  //   ellipsis: true,
  //   customRender: (_text, record) => {
  //     const text = record.InventorList?.join('、');
  //     return <div domPropsInnerHTML={text} style="white-space: pre-wrap;"></div>;
  //   },
  // },
];

// 相同软件著作权
export const SoftwareCopyrightColumns = [
  {
    title: '软件名称',
    dataIndex: 'Name',
  },
  {
    title: '版本号',
    dataIndex: 'VersionNo',
  },
  {
    title: '发布日期',
    dataIndex: 'PublishDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '软件简称',
    dataIndex: 'ShortName',
  },
  {
    title: '登记号',
    dataIndex: 'RegisterNo',
  },
  {
    title: '登记批准日期',
    dataIndex: 'RegisterAperDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
];

// 相互担保关联
export const GuarantorColumns = [
  {
    title: '担保方',
    dataIndex: 'Guarantee',
    ellipsis: true,
    scopedSlots: {
      customRender: 'qEntityLink',
    },
  },
  {
    title: '被担保方',
    ellipsis: true,
    dataIndex: 'Vouchee',
    scopedSlots: {
      customRender: 'qEntityLink',
    },
  },
  {
    title: '担保方式',
    width: 120,
    dataIndex: 'GuaranteeType',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '担保金额(万元)',
    dataIndex: 'GuaranteeMoney',
    width: 150,
    sorter: true,
    customRender: (item) => {
      return item ? (+item).toFixed(2) : '-';
    },
  },
  {
    title: '公告日期',
    sorter: true,
    width: 100,
    dataIndex: 'PublicDate',
    customRender: (data) => {
      return data ? moment(data * 1000).format('YYYY-MM-DD') : '-';
    },
  },
  {
    title: '内容',
    width: 60,
    scopedSlots: {
      customRender: 'guarantorDetail',
    },
  },
];

// 股权出质
export const EquityPledgeColumns = [
  {
    title: '登记编号',
    dataIndex: 'caseno',
    width: 160,
  },
  {
    title: '出质人',
    dataIndex: 'pledgorjson',
    scopedSlots: {
      customRender: 'qEntityLink',
    },
  },
  {
    title: '出质股权标的企业',
    scopedSlots: {
      customRender: 'EquityPledgeCom',
    },
  },
  {
    title: '质权人',
    dataIndex: 'pledgeejson',
    scopedSlots: {
      customRender: 'qEntityLink',
    },
  },
  {
    title: '出质股权数额',
    dataIndex: 'amountdesc',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'isvalid',
    customRender: (item) => {
      if (item) {
        return <QTag type="success">有效</QTag>;
      }
      return <QTag type="danger">失效</QTag>;
    },
  },
  {
    title: '登记日期',
    dataIndex: '',
    width: 100,
    customRender: (item) => {
      return moment(item.liandate * 1000).format('YYYY-MM-DD');
    },
  },
  {
    title: '内容',
    width: 60,
    scopedSlots: {
      customRender: 'gqczcontetnt',
    },
  },
];

export const JointBidIColumns = [
  {
    title: '企业名称',
    customRender: (text, record) => {
      return (
        <a href={`/embed/companyDetail?keyNo=${record.companyId}&title=${record.companyName}`} target="_blank">
          {record.companyName}
        </a>
      );
    },
  },
  {
    title: '关联企业名称',
    customRender: (text, record) => {
      return (
        <a href={`/embed/companyDetail?keyNo=${record.competitionCompanyId}&title=${record.competitionCompanyName}`} target="_blank">
          {record.competitionCompanyName}
        </a>
      );
    },
  },
  {
    title: '共同投标项目数',
    dataIndex: 'jointTenderCount',
    sorter: true,
    customRender: (text, record) => {
      if (!record.jointTenderCount) {
        return record.jointTenderCount;
      }
      return (
        <a
          onClick={async () => {
            openBidInfoDrawer(record);
          }}
        >
          {record.jointTenderCount}
        </a>
      );
    },
  },
  {
    title: '企业中标项目数',
    dataIndex: 'tenderCount',
    sorter: true,
    customRender: (text, record) => {
      if (!record.tenderCount) {
        return record.tenderCount;
      }
      return (
        <a
          onClick={() => {
            openBidInfoDrawer({ ...record, selectedCompanyId: record.companyId });
          }}
        >
          {record.tenderCount}
        </a>
      );
    },
  },
  {
    title: '关联企业中标数',
    dataIndex: 'competitionTenderCount',
    sorter: true,
    customRender: (text, record) => {
      if (!record.competitionTenderCount) {
        return record.competitionTenderCount;
      }
      return (
        <a
          onClick={() => {
            openBidInfoDrawer({ ...record, selectedCompanyId: record.competitionCompanyId });
          }}
        >
          {record.competitionTenderCount}
        </a>
      );
    },
  },
  {
    title: '企业中标率',
    dataIndex: 'tenderPercent',
    sorter: true,
    customRender: (text, record) => {
      return (
        <div class="flex" style={{ gap: '5px' }}>
          <span>{record.tenderPercent}</span>
          {record.tag && <QTag type="danger">{record.tag}</QTag>}
        </div>
      );
    },
  },
  {
    title: '关联企业中标率',
    dataIndex: 'competitionTenderPercent',
    sorter: true,
    customRender: (text, record) => {
      return (
        <div class="flex" style={{ gap: '5px' }}>
          <span>{record.competitionTenderPercent}</span>
          {record.competitionTag && <QTag type="danger">{record.competitionTag}</QTag>}
        </div>
      );
    },
  },
];

// 分支机构黑名单
export const CompanyBranchColumns = [
  {
    title: '被排查企业',
    width: 260,
    scopedSlots: { customRender: 'companyName' },
  },
  {
    title: '关联黑名单企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '关联链',
    scopedSlots: { customRender: 'path' },
  },
  {
    title: '图谱',
    width: 70,
    scopedSlots: { customRender: 'atlasAction' },
  },
];

// 资质筛查
export const QualificationCertificateColumns = [
  {
    title: '序号',
    width: 60,
  },
  {
    title: '企业名称',
    width: 360,
  },
  {
    title: '资质名称',
    align: 'center',
    width: 280,
    dataIndex: 'certificationName',
    scopedSlots: { customRender: 'certificationName' },
  },
  {
    title: '证书编号',
    align: 'center',
    width: 280,
    dataIndex: 'certificateNo',
    scopedSlots: { customRender: 'certificateNo' },
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
  },
  {
    title: '有效期',
    width: 200,
    scopedSlots: { customRender: 'validDate' },
  },
  {
    title: '状态',
    width: 150,
    dataIndex: 'status',
    scopedSlots: { customRender: 'certificationStatus' },
  },
];

export const BidCollusiveListColumns = [
  {
    title: '关联企业名称',
    scopedSlots: { customRender: 'bidConclusiveRelatedConpany' },
  },
  {
    title: '项目名称',
    customRender: (record) => {
      const data = JSON.parse(record.projectinfo)?.[0];
      return data?.ProjectName ?? '-';
    },
  },
  {
    title: '项目编号',
    customRender: (record) => {
      const data = JSON.parse(record.projectinfo)?.[0];
      return data?.ProjectNo ?? '-';
    },
  },
  {
    title: '判断依据',
    customRender: (record) => {
      // 直接关系
      if (record.collusiverisktype === 1) {
        if (record.sourcetype === 5) {
          // 通过数据source_id是否相同判断
          const uniqSourceIds = uniq(record.sourceid);
          return uniqSourceIds.length === 1
            ? '同一条黑名单数据中，提及多家处罚企业，且被认定为有围串标关系'
            : '多条黑名单数据中，处罚单位、处罚原因、移入日期、数据来源完全一致，且被认定为有围串标关系';
        }
        return '同一行政处罚中涉及多家被处罚企业，且被认定为有围串标关系';
      }
      // 疑似关系
      if (record.sourcetype === 5) {
        return '多条黑名单数据中，处罚单位、处罚原因一致，移入日期前后间隔2天内，且被认定为有围串标关系';
      }
      return '多条行政处罚中涉及多家被处罚企业，提及相同或相似的项目编号/项目名称，且被认定为有围串标关系';
    },
  },
  {
    title: '案号/决定文书号',
    scopedSlots: {
      customRender: 'bidCollusiveDocs',
    },
  },
  {
    title: '数据来源',
    dataIndex: 'sourcetype',
    width: 120,
    customRender: (source) => {
      return SourceTypes[source] ?? '-';
    },
  },
  {
    title: '内容',
    scopedSlots: {
      customRender: 'bidCollusiveListContent',
    },
  },
];

export const DirectConnectionColumns = [
  {
    title: '黑名单企业名称',
    customRender: (text, record) => {
      return (
        <QEntityLink
          ellipsis={false}
          coyObj={{
            Name: record.companyNameDD,
            KeyNo: record.companyKeynoDD,
          }}
        ></QEntityLink>
      );
    },
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
    width: 400,
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '列入时间',
    dataIndex: 'joinDate',
    width: 200,
    customRender: (item) => {
      return moment(item * 1000).format('YYYY-MM-DD');
    },
  },
  {
    title: '黑名单有效期',
    dataIndex: 'duration',
    width: 200,
    customRender: (data) => {
      return blacklistDurationFormatter(data);
    },
  },
  {
    title: '截止时间',
    dataIndex: 'expiredDate',
    width: 200,
    customRender: (data) => {
      if (!data) {
        return '不限';
      }
      return moment(data).format('YYYY-MM-DD');
    },
  },
];
export const BlackListInvestigationsColumns = [
  {
    title: '企业名称',
    width: 260,
    customRender: (text, record) => {
      return (
        <QEntityLink
          ellipsis={false}
          coyObj={{
            Name: record.startCompanyName,
            KeyNo: record.startCompanyKeyno,
          }}
        ></QEntityLink>
      );
    },
  },
  {
    title: '关联黑名单企业名称',
    width: 260,
    customRender: (text, record) => {
      return (
        <QEntityLink
          ellipsis={false}
          coyObj={{
            Name: record.companyNameRelated,
            KeyNo: record.companyKeynoRelated,
          }}
        ></QEntityLink>
      );
    },
  },
  {
    title: '关联路径详情',
    scopedSlots: { customRender: 'blackPath' },
  },
  {
    title: '图谱',
    width: 78,
    scopedSlots: { customRender: 'blackRenderAtlasChart' },
  },
];

// 被列入严重违法失信企业名录
export const CompanyCreditColumns = [
  {
    title: '列入对象',
    scopedSlots: {
      customRender: 'companyName',
    },
  },
  {
    title: '列入日期',
    width: 103,
    dateFormat: true,
    dataIndex: 'AddDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '作出决定机关(列入)',
    dataIndex: 'AddOffice',
  },
  {
    title: '列入严重违法失信企业名单原因',
    dataIndex: 'AddReason',
  },
];

// 被列入失信被执行人
export const PersonCreditCurrentColumns = [
  {
    title: '企业名称',
    scopedSlots: {
      customRender: 'companyName',
    },
  },
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: () => {
      return (
        <div>
          <span>疑似申请执行人</span>
          <Tooltip
            overlayClassName="self-customed"
            title="疑似申请执行人是基于司法案件的基础数据维度综合分析得出，仅供参考，并不代表企查查任何明示、暗示之观点或保证"
          >
            <QIcon type="icon-icon_zhushi" style="color: #d8d8d8;margin-left: 4px;"></QIcon>
          </Tooltip>
        </div>
      );
    },
    dataIndex: 'SqrInfo',
    scopedSlots: { customRender: 'qentityArr' },
  },
  {
    title: '执行法院',
    width: 170,
    dataIndex: 'ExecuteGov',
  },
  {
    title: () => {
      return (
        <div>
          <span>涉案金额(元)</span>
          <Tooltip
            overlayClassName="self-customed"
            title="涉案金额是基于相关生效法律文书确定义务分析得出，仅供参考，并不代表企查查任何明示、暗示之观点或保证。"
          >
            <QIcon type="icon-icon_zhushi" style="color: #d8d8d8;margin-left: 4px;"></QIcon>
          </Tooltip>
        </div>
      );
    },
    width: 120,
    sorter: true,
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '履行情况',
    width: 90,
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.executeStatus || item.ExecuteStatus || '-',
        },
      };
    },
  },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    title: '立案日期',
    sorter: true,
    dataIndex: 'LiAnDate',
    width: 103,
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '发布日期',
    width: 103,
    dataIndex: 'PublicDate',
    sorter: true,
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 税收违法
export const TaxationOffencesColumns = [
  {
    title: '企业名称',
    scopedSlots: {
      customRender: 'entityLink',
    },
    entity: {
      keyNo: 'keyno',
      name: 'name',
    },
  },
  {
    title: '发布日期',
    dataIndex: 'publishdate',
    width: 103,
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '所属税务机关',
    dataIndex: 'court',
  },
  {
    title: '案件性质',
    dataIndex: 'removereason',
  },
  {
    title: '主要违法事实',
    dataIndex: 'casereason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: () => {
      return (
        <div>
          <span>罚款金额(元)</span>
          <Tooltip overlayClassName="self-customed" title="该数据从公示结果解析得出，仅供参考，并不代表企查查任何明示、暗示之观点或保证。">
            <QIcon type="icon-icon_zhushi" style="color: #d8d8d8;margin-left: 4px;"></QIcon>
          </Tooltip>
        </div>
      );
    },
    dataIndex: 'amount',
    sorter: true,
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '法律依据及处理处罚情况',
    dataIndex: 'title',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 假冒国企
export const FakeSOESColumns = [
  {
    title: '被排查企业',
    scopedSlots: {
      customRender: 'companyName',
    },
  },
  {
    title: '被列入假冒国企企业名称',
    scopedSlots: {
      customRender: 'fakeSoeName',
    },
  },
  {
    title: '关联路径',
    width: '50%',
    scopedSlots: {
      customRender: 'fakePath',
    },
  },
];
