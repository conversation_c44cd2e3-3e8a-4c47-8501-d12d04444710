import { createLocalVue, mount, shallowMount } from '@vue/test-utils';

import { task as taskService } from '@/shared/services';
import { hasPermission } from '@/shared/composables/use-permission';

import TaskPage from '../task.page';

vi.mock('vue-router/composables');
vi.mock('@/shared/services');
vi.mock('@/shared/composables/use-permission');
vi.mock('validator/es/lib/isEmail', () => ({
  default: vi.fn(() => true),
}));

vi.mock('vue-router/composables', function () {
  return {
    __esModule: true,
    useRouter() {
      return {
        replace: vi.fn().mockResolvedValue({}),
      };
    },
    useRoute() {
      return {};
    },
  };
});

const localVue = createLocalVue();

describe('TaskPage', () => {
  beforeEach(() => {
    vi.mocked(taskService.search).mockResolvedValue({ data: [] });
    vi.mocked(taskService.userTaskList).mockResolvedValue([{ userId: 1, name: 'User1' }]);
    vi.mocked(hasPermission).mockReturnValue(true);
  });

  // 测试组件是否正确挂载
  it('should mount correctly', () => {
    const wrapper = mount(TaskPage, {
      localVue,
      propsData: {
        pageType: 'import-task-list',
      },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });

  // 测试handleFilterChange方法是否正确更新filters和路由
  it('should update filters and route when handleFilterChange is called', async () => {
    const payload = { filters: { businessType: 'test' } };
    const wrapper = shallowMount(TaskPage, {
      propsData: {
        pageType: 'import-task-list',
      },
    });
    wrapper.vm.handleFilterChange(payload, {});
    expect(wrapper.vm.filters).toEqual(payload);
  });

  // 测试onMounted钩子是否调用了getUserList
  it('should call getUserList on mounted', async () => {
    const wrapper = shallowMount(TaskPage, {
      propsData: {
        pageType: 'import-task-list',
      },
    });
    await wrapper.vm.$nextTick();
    expect(taskService.userTaskList).toHaveBeenCalled();
  });
});
