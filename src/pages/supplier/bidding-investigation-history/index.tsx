import { defineComponent, ref, onMounted, computed, nextTick } from 'vue';
import { debounce } from 'lodash';
import { useRoute, useRouter } from 'vue-router/composables';
import { message } from 'ant-design-vue';

import { biddings, task as taskService } from '@/shared/services';
import CommonSearchFilter from '@/components/common/common-search-filter';
import HeroicLayout from '@/shared/layouts/heroic';
import { getTranslateZeissFilterGroups } from '@/shared/composables/use-translate';
import { useMenuStore } from '@/hooks/use-menu-store';
import QCard from '@/components/global/q-card';
import { useCacheQuery } from '@/hooks/use-cache-query';
import { isJSONString } from '@/utils/data-type/is-json-string';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import CommonResult from '@/shared/components/common-result';
import { useRoomSocket } from '@/hooks/use-room-socket/use-room-socket';
import { EXPORTITEMS } from '@/config/record.config';
import DropdownButtonWrapper from '@/components/dropdown-button-wrapper';
import { useCommonSettingStore } from '@/hooks/use-common-settings';
import { useTrackEvent } from '@/hooks/use-track-event';
import { openTenderRemarkDrawer } from '@/shared/components/tender-remark-history';
import { useBiddingDetail } from '@/shared/composables/use-bidding-detail';
import ColumnSort from '@/shared/components/column-sort/column-sort';

import { DILIGENCE_SEARCH_FILTER_GROUPS } from '../bidding-investigation/config';
import { detailColoumns } from './config';

const BiddingInvestigationHistoryPage = defineComponent({
  name: 'BiddingInvestigationHistoryPage',
  setup() {
    const filterGroups = ref(getTranslateZeissFilterGroups(DILIGENCE_SEARCH_FILTER_GROUPS));
    const { search, result } = useBiddingDetail();
    const init = ref(true);

    const { dataSource, selectedIds, sortInfo, columnsOri, generateSelectData, dynamicColumns, isFixed } = useCommonSettingStore({
      idKey: 'id',
      key: 'bidding-investigation-history',
      columnData: detailColoumns(),
    });

    const tableRef = ref(null);

    const route = useRoute();
    const router = useRouter();
    const track = useTrack();

    const { handleSearchTrack } = useTrackEvent('招标排查历史记录');

    const totalCount = ref(0);

    /** 从URL中获取日期区间 */
    const dateRangeFromUrlQuery = isJSONString(route.query?.dateRange as string)
      ? JSON.parse(route.query?.dateRange as string)
      : { currently: true, flag: 1, number: 30, unit: 'day' };

    /** 从URL中获取TenderNo， */
    const tenderNoFromUrlQuery = route.query?.tenderNo || undefined;
    // 是否使用上次缓存的查询条件，不使用就清除上次缓存
    const DEFAULT_SEARCH_CONDITIONS = Object.freeze({
      query: {
        keywords: tenderNoFromUrlQuery,
        // 如果有tenderNO，上面的日期就不能默认3个月,不然从权益中心跳过来的数据很可能找不到
        filters: { sd: tenderNoFromUrlQuery ? undefined : dateRangeFromUrlQuery },
      },
      pagination: { pageSize: 10, pageIndex: 1, total: 0 },
      sort: {},
    });

    const { cached } = useCacheQuery(
      // Namespace
      'bidding-investigation-history',
      // Defaults
      DEFAULT_SEARCH_CONDITIONS,
      // Deps
      { route, router }
    );

    // 提交人列表
    const getUserList = async () => {
      try {
        const res = await taskService.userlist('DueDiligenceTender');
        filterGroups.value = filterGroups.value.map((item: any) => {
          const children = item.children.map((child: any) => {
            if (child.field === 'operators') {
              return {
                ...child,
                options: (res ?? []).map((user: any) => ({
                  value: user?.userId,
                  label: user?.name,
                })),
              };
            }
            return child;
          });
          return {
            ...item,
            children,
          };
        });
      } catch (error) {
        console.error(error);
      }
    };
    const getParams = computed(() => {
      const { keywords, filters = {} } = cached.query.value;
      return {
        searchKey: keywords,
        createDate: filters?.sd ? [filters?.sd] : undefined,
        status: filters?.status,
        operators: filters?.operators,
        ...cached.sort.value,
        ...cached.pagination.value,
      };
    });

    const handleFilterChange = (payload, group) => {
      cached.query.value = payload;
      cached.pagination.value.pageIndex = 1;
      handleSearchTrack(6941, { keyword: cached.query.value.keywords, filter: group.label });
    };
    // 更新cache的数据
    const update = ({ type, value }) => {
      const { total, data, pageIndex, pageSize } = value;
      switch (type) {
        case 'init':
          init.value = false;
          dataSource.value = data;
          cached.pagination.value.pageIndex = pageIndex;
          cached.pagination.value.pageSize = pageSize;
          totalCount.value = total;
          break;
        case 'pagination':
          cached.pagination.value.pageIndex = pageIndex;
          cached.pagination.value.pageSize = pageSize;
          break;
        default:
          Object.assign(sortInfo, value);
      }
    };
    const socketUpdater = debounce(async (data) => {
      if (data.status !== '0') {
        await nextTick();
        tableRef.value.search();
      }
    }, 300);
    useRoomSocket('/rover/socket', {
      eventType: 'TenderDiligenceChanged',
      filter: (messageData) => messageData.roomType === 'TenderDiligence',
      update: socketUpdater,
      refresh: socketUpdater,
    });

    const handleRetry = async (data) => {
      try {
        if (!data.id) {
          throw new Error('id not found');
        }
        await biddings.retry(data.id);
        message.success('重试成功');
        track(createTrackEvent(6943, '招标排查历史记录', '重试'));
        tableRef.value.search();
      } catch (err) {
        console.error(err);
      }
    };

    const handleExportAll = async () => {
      try {
        await biddings.export(getParams.value);
        message.success('正在导出，稍后可前往任务列表查看进度');
      } catch (error) {
        console.error(error);
      }
    };

    const handleExportByIds = async () => {
      try {
        if (!selectedIds.value.length) {
          message.warning('请选择需要导出的记录');
          return;
        }
        await biddings.export({ diligenceIds: selectedIds.value });
        message.success('正在导出，稍后可前往任务列表查看进度');
      } catch (error) {
        console.error(error);
      }
    };

    const handleExport = async (key: string) => {
      if (key === 'export') {
        await handleExportAll();
        track(createTrackEvent(6943, '招标排查历史记录', '全部导出'));
      } else {
        await handleExportByIds();
        track(createTrackEvent(6943, '招标排查历史记录', '选中导出'));
      }
    };

    const openRemarkDrawer = async (record) => {
      await search(record.id);
      openTenderRemarkDrawer({
        title: `排查记录 - ${result.value?.projectName}（${result.value?.projectNo}）`,
        data: result.value.remark,
      });
    };

    onMounted(async () => {
      await getUserList();
      init.value = false;
    });

    return {
      filterGroups,
      init,
      dataSource,
      totalCount,
      cached,
      sorter: cached.sort,
      filters: cached.query,
      getParams,
      columnsOri,
      dynamicColumns,
      handleFilterChange,
      update,
      tableRef,
      handleRetry,
      selectedIds,
      handleExport,
      generateSelectData,
      openRemarkDrawer,
      isFixed,
    };
  },
  render() {
    const { currentTitle } = useMenuStore();
    return (
      <HeroicLayout loading={this.init}>
        <QCard
          slot="hero"
          title={currentTitle.value}
          bodyStyle={{
            paddingTop: 0,
          }}
        >
          <CommonSearchFilter
            placeholder="支持按照项目名称、项目编号、排查编号、企业名称查询"
            inputWidth={'450px'}
            filterConfig={this.filterGroups}
            onChange={this.handleFilterChange}
            defaultValue={this.filters}
          />
        </QCard>
        <CommonResult
          ref="tableRef"
          rowKey={'id'}
          searchKey={this.filters?.keywords}
          searchFn={biddings.getTenderRecord}
          sorter={this.sorter}
          columns={this.dynamicColumns}
          scroll={{
            x: true,
            y: 'calc(100vh - 146px - 220px)',
          }}
          showIndex={true}
          isFixed={this.isFixed}
          needSelect
          selectedIds={this.selectedIds}
          useCache={true}
          filterParams={this.getParams}
          onUpdateCache={this.update}
          onRetry={this.handleRetry}
          onSelect={this.generateSelectData}
          onOpenRemark={this.openRemarkDrawer}
          onAction={(record) => {
            this.$track(createTrackEvent(6943, '招标排查历史记录', '详情'));
            this.$router.push({
              name: 'bidding-investigation-detail',
              params: {
                type: 'bidding-investigation-history',
              },
              query: {
                id: record?.id,
                from: 'record',
              },
            });
          }}
        >
          <ColumnSort
            slot="columnSort"
            class="sort-icon"
            colums={this.columnsOri}
            onChange={(data) => {
              this.columnsOri = data;
            }}
            nativeOnClick={() => {
              this.$track(createTrackEvent(6226, '排查记录', '设置icon'));
            }}
          />
          <div slot="extra">
            <DropdownButtonWrapper
              totalCount={this.totalCount}
              v-permission={[2116]}
              btnText="导出列表"
              needPopConfirm={false}
              menuItems={EXPORTITEMS}
              selectIdlength={this.selectedIds.length}
              onConfirm={this.handleExport}
            />
          </div>
        </CommonResult>
      </HeroicLayout>
    );
  },
});

export default BiddingInvestigationHistoryPage;
