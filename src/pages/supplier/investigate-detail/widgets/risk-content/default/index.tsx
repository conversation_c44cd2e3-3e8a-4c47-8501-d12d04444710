import { computed, defineComponent, PropType, unref } from 'vue';

import RiskAction from '@/shared/components/risk-action';
import { uesInvestStore } from '@/hooks/use-invest-store';
import QCard from '@/components/global/q-card';
import FullWatermark from '@/components/full-watermark';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { riskTypeMap } from '@/shared/constants/risk-type.config';
import { useUserStore } from '@/shared/composables/use-user-store';

import RiskFilter from '../../risk-filter';
import BasicInfo from '../../basic-info/default';
import RiskReview from '../../risk-review';
import { openSettingDrawer } from './setting-drawer';
import RiskQualification, { type CertificationObj } from '../../risk-qualification';
import AIReportGuide from '../../ai-report-guide';

const RiskContentDefault = defineComponent({
  props: {
    loading: {
      type: Boolean,
      default: true,
    },
    dimensions: {
      type: Array as PropType<Record<string, any>>,
      default: () => [],
    },
    company: {
      type: Object,
      required: true,
    },
    tabs: {
      type: Array as PropType<Record<string, any>>,
      default: () => [],
    },
    riskLevel: {
      type: Number,
      required: true,
    },
    qualifications: {
      type: Object as PropType<CertificationObj>,
      default: () => ({}),
    },
    credits: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    /**
     * 距离页面的距离
     */
    offset: {
      type: Object as PropType<{ x: number; y: number }>,
      default: () => ({ x: 0, y: 0 }),
    },
    /**
     * 面包屑的高度
     */
    breadcrumbHeight: {
      type: Number,
      default: 0,
    },
    partnerTags: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    /**
     * 是否为嵌入页面
     */
    isExternal: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const { riskInfo } = uesInvestStore();
    const track = useTrack();
    const handleOpenSettingModal = async () => {
      track(createTrackEvent(6204, '准入排查详情页', '关联设置'));
      await openSettingDrawer({ id: unref(riskInfo).orgSettingsId });
    };

    const dimensionsWithLabel = computed(() => props.dimensions.map((dm) => ({ ...dm, label: riskTypeMap[dm.groupKey] })));

    // Watermark size
    const watermarkHeight = computed(() => window.innerHeight - props.offset.y);

    const { isInternal } = useUserStore();

    return {
      handleOpenSettingModal,
      riskInfo,
      watermarkHeight,
      dimensionsWithLabel,
      isInternal,
    };
  },
  render() {
    const props = this;
    if (props.loading) {
      return <div style="height: 0" />;
    }

    return (
      <div>
        {/* 基础信息(工商) */}
        <QCard
          rootStyle={{
            paddingBottom: '15px',
            borderRadius: props.tabs.length > 1 ? '0 0 4px 4px' : '4px',
          }}
        >
          <BasicInfo company={props.company} tags={props.partnerTags}>
            <template slot="extra">{this.$slots?.extra}</template>
          </BasicInfo>
        </QCard>

        {/* 资质信息 */}
        <RiskQualification qualifications={props.qualifications} credits={props.credits} />

        {this.dimensionsWithLabel.length > 0 ? (
          <FullWatermark
            visible={!!this.riskInfo.snapshotId && !this.isExternal}
            height={this.watermarkHeight}
            offset={{
              x: props.offset.x,
              y: props.offset.y,
            }}
            fillOffset={{
              x: 300,
              y: 300,
            }}
          >
            {/* AI解读分析模块 */}
            {this.riskInfo.id ? <AIReportGuide diligenceId={this.riskInfo.id} /> : null}

            <QCard rootStyle={{ marginTop: '10px' }} bodyStyle={{ padding: '15px 15px 15px 30px' }}>
              <div slot="title">
                <span class={'flex'}>
                  排查结果
                  <q-glossary-info
                    style={{
                      transform: 'translateY(-1px)',
                      marginLeft: '5px',
                    }}
                    placement={'bottomLeft'}
                    contentStyle={{
                      width: '420px',
                      maxWidth: '420px',
                    }}
                    tooltip="排查结果依据用户根据自身实际业务需求所设置的排查模型而生成，以辅助业务决策判断，并不代表企查查方的任何明示、暗示之观点或保证。"
                  >
                    <q-icon type="icon-icon_tswb" slot="trigger" />
                  </q-glossary-info>
                </span>
              </div>
              <div slot="extra">
                <RiskAction onClick={this.handleOpenSettingModal} icon="icon-tuisongshezhi1" v-show={this.riskInfo.orgSettingsId}>
                  <q-glossary-info tooltip="当前排查结果关联的模型参数设置">
                    <span slot="trigger">关联设置</span>
                  </q-glossary-info>
                </RiskAction>
              </div>
              {/* 排查结果 */}
              <RiskReview scrollOffsetTop={this.breadcrumbHeight} />
            </QCard>

            {/* 排查详情 */}
            <QCard title="排查详情">
              <RiskFilter dimensions={this.dimensionsWithLabel} />
            </QCard>
          </FullWatermark>
        ) : null}
      </div>
    );
  },
});

export default RiskContentDefault;
