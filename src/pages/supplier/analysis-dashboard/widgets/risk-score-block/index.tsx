import { type PropType, defineComponent, computed } from 'vue';
import { chunk } from 'lodash';
import { Tooltip } from 'ant-design-vue';

import Icon from '@/shared/components/icon';
import { useDOMScreenshot } from '@/hooks/use-dom-screenshot';
import { useUserStore } from '@/shared/composables/use-user-store';
import { useI18n } from '@/shared/composables/use-i18n';
import QIcon from '@/components/global/q-icon';
import { createTrackEvent, useTrack } from '@/config/tracking-events';

import RiskScore from '../risk-score';
import styles from './risk-score-block.module.less';

const RiskScoreBlock = defineComponent({
  props: {
    value: {
      type: Array as PropType<Record<string, any>[]>,
      required: true,
      default: () => [],
    },
  },
  setup(props) {
    const { tc } = useI18n();
    const [rootRef, downloadScreenshot] = useDOMScreenshot();
    const chunkData = computed(() => {
      const { isZeiss } = useUserStore();
      const sliceData = props.value.slice(0, isZeiss.value ? 6 : 9);
      return chunk(sliceData, 3);
    });

    const track = useTrack();
    const handleClick = () => {
      track(createTrackEvent(6903, '分析看板', '下载', '维度分析'));
      downloadScreenshot();
    };

    return {
      tc,
      rootRef,
      chunkData,
      handleClick,
    };
  },
  render() {
    const { tc } = this;
    return (
      <div>
        <div class={styles.title}>
          {tc('Dimensional Analysis')}
          <Tooltip overlayClassName={styles.tooltip} placement={'bottomLeft'}>
            <div slot="title" style={{ fontSize: '14px', lineHeight: '22px' }}>
              <div class="flex" style={{ marginBottom: '5px' }}>
                <span style={{ whiteSpace: 'nowrap' }}>
                  <q-icon type="icon-zhanbi"></q-icon>
                  <span style={{ marginLeft: '2px' }}>：</span>
                </span>
                <span>{tc('dimensionalAnalysisSymbol.riskPercentage')}</span>
              </div>
              <div class="flex">
                <span style={{ whiteSpace: 'nowrap' }}>
                  <q-icon type="icon-zengchangshuai"></q-icon>
                  <span style={{ marginLeft: '2px' }}>：</span>
                </span>
                <span>{tc('dimensionalAnalysisSymbol.increaseCount')}</span>
              </div>
            </div>
            <QIcon
              style={{
                marginLeft: '5px',
                cursor: 'pointer',
                color: '#d8d8d8',
              }}
              type="icon-zhushi"
            />
          </Tooltip>
        </div>
        <div class={styles.containerWrapper} ref="rootRef">
          <div class={styles.download} data-capture="exclude" onClick={this.handleClick}>
            <Icon type="icon-xiazai" />
          </div>
          {this.chunkData?.map((items, dataIndex) => (
            <div class={styles.container} key={`chunk-${dataIndex}`}>
              {items?.map((item, index) => {
                const placement = { 0: 'bottomLeft', 1: 'bottom', 2: 'bottomRight' }[index];
                return <RiskScore placement={placement} item={item} />;
              })}
            </div>
          ))}
        </div>
      </div>
    );
  },
});

export default RiskScoreBlock;
