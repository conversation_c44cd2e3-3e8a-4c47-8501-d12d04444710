import { mount } from '@vue/test-utils';

import SuggestionInput from '..';
import Icon from '@/shared/components/icon';

const ICONOSST = {
  clearIcon: 'clear',
  suffixIcon: 'suffix',
};

describe('SuggestionInput', () => {
  afterEach(() => {
    vi.restoreAllMocks(); // 执行清理 mock
  });

  test('render', async () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        value: 'VALUE',
        ...ICONOSST,
      },
    });
    await wrapper.vm.$nextTick();
    expect(wrapper).toMatchSnapshot();
  });

  test('props: value', () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        value: 'VALUE',
        ...ICONOSST,
      },
    });

    const input = wrapper.findComponent({ name: 'AInput' });
    expect(input.exists()).toBe(true);
    expect(input.vm.$props.value).toBe('VALUE');
  });

  test('props: hasBulkSearch', () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        hasBulkSearch: false,
        ...ICONOSST,
      },
    });
    expect(wrapper.text()).not.toContain('批量排查');
  });

  test('internal: history', async () => {
    const storageReader = vi.spyOn(window.localStorage, 'getItem');
    const wrapper = mount(SuggestionInput, {
      propsData: {
        value: 'VALUE',
        storageKey: '__SEARCH_HISTORY__',
        hasBulkSearch: true,
        ...ICONOSST,
      },
    });
    const input = wrapper.findComponent({ name: 'AInput' });
    expect(input.exists()).toBe(true);
    expect(storageReader).toBeCalledWith('__SEARCH_HISTORY__');
  });

  test.todo('events: clear', async () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        value: 'VALUE',
        ...ICONOSST,
      },
    });
    const clearButton = wrapper.findAllComponents(Icon).at(0);
    expect(clearButton.attributes('type')).toBe('clear');

    // 点击清空按钮
    await clearButton.trigger('click');
    // FIXME: 期望触发 clear 事件
    // expect(wrapper.emitted('clear')).toBeTruthy();
  });

  test.todo('events: suffixClick', async () => {
    const wrapper = mount(SuggestionInput, {
      propsData: {
        value: 'VALUE',
        hasBulkSearch: true,
        ...ICONOSST,
      },
    });
    const suffixClearButton = wrapper.findAllComponents(Icon).at(1);
    expect(suffixClearButton.attributes('type')).toBe('suffix');
    // 点击清空按钮
    await suffixClearButton.trigger('click');
    // 期望触发 suffixClear 事件
    // expect(wrapper.emitted('suffixClick')).toBeTruthy();
  });
});
