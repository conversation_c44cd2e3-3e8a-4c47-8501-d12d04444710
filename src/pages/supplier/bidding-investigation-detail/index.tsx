import { defineComponent, onMounted, computed } from 'vue';
import { message, Spin, Tooltip } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router/composables';
import axios from 'axios';

import FullWatermark from '@/components/full-watermark';
import { useDOMScreenshot } from '@/hooks/use-dom-screenshot';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import RiskAction from '@/shared/components/risk-action';
import { useAbility } from '@/libs/plugins/user-ability';
import { biddings as biddingsService } from '@/shared/services';
import { scrollTo, useBiddingDetail } from '@/shared/composables/use-bidding-detail';

import InvestigationBreadcrumb from './components/investigation-breadcrumb';
import TenderTitleBlock from './components/tender-title-block';
import TenderResultBlock from './components/tender-result-block';
import TenderDimensionBlock from './components/tender-dimension-block';
import styles from './bidding-investigation-detail.module.less';

const BiddingInvestigationDetailPage = defineComponent({
  name: 'BiddingInvestigationDetailPage',
  setup() {
    const { search, refreshSnapshot, updateTenderTitle, result, dimensionHitsDetails, loading } = useBiddingDetail();

    const route = useRoute();
    const router = useRouter();
    onMounted(async () => {
      try {
        await search();
      } catch (error) {
        if (axios.isAxiosError(error) && error.response?.data?.code === 900100) {
          router.replace({
            name: 'bidding-investigation-failed',
            params: route.params,
            query: { ...route.query, auth: 'failed' },
          });
        }
      }
    });

    const [pageRef, downloadScreenshot] = useDOMScreenshot();

    /** 生成快照 */
    const ability = useAbility();
    const track = useTrack();
    const handleGenerateReport = async () => {
      if (!(await ability.check('stock', ['DiligenceReportQuantity']))) {
        return;
      }
      if (!route.query.id) {
        message.warning('数据异常，请刷新重试');
        return;
      }
      await biddingsService.generateReport(route.query.id);
      message.success('您的报告正在生成中，成功后我们将第一时间提醒您');
      track(createTrackEvent(6933, '招标排查详情', '生成报告'));
    };

    // Watermark size
    const watermarkHeight = computed(() => window.innerHeight - 102);
    return {
      result,
      dimensionHitsDetails,
      loading,
      pageRef,
      downloadScreenshot,
      watermarkHeight,
      search,
      refreshSnapshot,
      updateTenderTitle,
      handleGenerateReport,
    };
  },

  render() {
    const renderContent = () => {
      if (this.loading) {
        return (
          <section class={styles.loadingContainer}>
            <Spin spinning />
          </section>
        );
      }

      return (
        <FullWatermark
          height={this.watermarkHeight}
          offset={{
            x: 190,
            y: 102,
          }}
          fillOffset={{
            x: 300,
            y: 300,
          }}
        >
          <section ref="pageRef">
            {/* 基础信息 */}
            <TenderTitleBlock
              dataSource={this.result}
              onSearch={this.search}
              onUpdateTitle={this.updateTenderTitle}
              saveFn={biddingsService.addRemark}
            >
              <template slot="extra">
                <Tooltip title="点击将刷新页面重新排查">
                  <RiskAction
                    data-capture="exclude"
                    icon="icon-icon_sqq"
                    v-debounceclick={() => {
                      this.refreshSnapshot();
                      this.$track(createTrackEvent(6933, '招标排查详情', '重新排查'));
                    }}
                    theme="slight"
                  >
                    重新排查
                  </RiskAction>
                </Tooltip>

                <RiskAction data-capture="exclude" onClick={this.downloadScreenshot} icon="icon-icon_gongshangkuaizhao">
                  生成快照
                </RiskAction>

                <RiskAction data-capture="exclude" v-permission={[2115]} icon="icon-shengchengbaogao1" onClick={this.handleGenerateReport}>
                  生成报告
                </RiskAction>
              </template>
            </TenderTitleBlock>

            {/* 招标排查排查结果: 排查企业明细 */}
            <TenderResultBlock
              dataSource={this.result}
              dimensionHitsDetails={this.dimensionHitsDetails}
              style={{ marginTop: '15px' }}
              onSearch={this.search}
            ></TenderResultBlock>

            {/* 招标排查维度详情页 */}
            <TenderDimensionBlock
              navOffsetTop="35px"
              dataSource={this.result}
              dimensionHitsDetails={this.dimensionHitsDetails}
              onScrollTo={scrollTo}
              style={{ marginTop: '15px' }}
            />
          </section>
        </FullWatermark>
      );
    };

    return (
      <div class={styles.container}>
        {/* Render: breadcrumb */}
        <InvestigationBreadcrumb />

        {/* Watermark wrapper */}
        {renderContent()}
      </div>
    );
  },
});

export default BiddingInvestigationDetailPage;
