import { get } from 'lodash';
import { getCurrentInstance } from 'vue';

/**
 * 风险动态维度->风险维度详情映射
 */
const DYNAMIC_RISK_DIMENSION_MAP = {
  MonitorUpdateLegalPerson: 'oper', // 法定代表人
  MonitorUpdatePerson: 'actualController', // 实际控制人
  MonitorUpdateHolder: 'holderChange', // 股东变更
  MonitorMajorShareHolderChanged: 'bigStockChange', // 大股东变更
  MonitorUpdateBeneficiary: 'finalBeneficiary', // 受益所有人
  MonitorUpdateName: 'entNameChange', // 企业名称变更
  MonitorUpdateAddress: 'entAddress', // 地址变更
  MonitorUpdateRegisteredCapital: 'entAddress',

  MonitorBankruptcy: 'bankruptcy', // 破产重整
  MonitorDishonestDebtor: 'shixin', // 失信被执行人
  MonitorPersonExecution: 'zhixing', // 被执行人
  MonitorFinalCase: 'endExecutionCase', // 终本案件
  MonitorEquityFreeze: 'assistance', // 股权冻结

  MonitorTaxationOffences: 'taxIllegal',
  MonitorTaxArrearsNotice: 'owenotice', // 欠税公告
  MonitorSpotCheck: 'spotcheck', // 抽查检查
  MonitorUnauthorizedEntry: 'notallowedentry',
  MonitorCancellationOfFiling: 'enliqDetail',
  MonitorSimpleCancellation: 'jyzx', // 简易注销
  MonitorBillDefaults: 'billDefaults',
  MonitorChattelMortgage: 'mPledge',
  MonitorLandMortgage: 'landmortgage',
  MonitorEquityPledge: 'pledge',
  MonitorEquityPawn: 'spledge',
  MonitorGuaranteeInfo: 'riskGuarantor',
  MonitorFoodSafety: 'foodSafetyDetail',
  MonitorRestrictedGoingAbroad: 'passportlimit', //未准入境

  // MonitorPublicSecurityNotice: 'publicSecurity', // 公安通告
  MonitorBlacklist: 'govProcurementIllegal', // 黑名单
  BondDefault: 'bond', // 新增债券违约
  NoticeInTimePeriod: 'ktnotice', // 开庭公告
  FilingInformation: 'lian', // 新增立案信息
  MonitorTaxReminder: 'monitorTaxReminder', // 税务催报
  MonitorTaxCall: 'monitorTaxCall', // 税务催缴
  MonitorReductionOfCapital: 'decreaseCapiNotice', // 减资公告
  MonitorBusinessScopeChange: 'manageScope', // 经营范围变更
  MonitorForeignInvestmentChange: 'outboundInvest', // 对外投资变更
  MonitorMovablePropertySeizure: 'chattelSeizure', // 动产查封
};

/** 无详情维度 */
const DYNAMIC_RISK_DIMENSION_NO_DETAIL = [
  'MonitorSeriousIllegality', // 严重违法
  'MonitorBusinessAbnormal', // 经营异常
  'MonitorAbnormalHousehold', // 经营异常
  'MonitorBusinessStatus', // 被列入非正常户
  'MonitorCertificationExpired', // 资质证书过期提醒 额外判断
  'QccCreditChange', // 企查分变化
];
/**
/** 无需处理维度 */
const DYNAMIC_RISK_DIMENSION_NO_HANDEL = [
  'NoticeInTimePeriod', // 短期多起开庭公告
];
/**
 * 详情是否为弹窗
 * @param dimensionKey
 * @returns
 */
const hasDynamicRiskDimensionDetailModal = (dimensionKey?: string): boolean => {
  return DYNAMIC_RISK_DIMENSION_MAP[dimensionKey] !== undefined;
};

/**
 * 是否有详情
 * @returns
 */
export const noDynamicRiskDimensionDetail = (key) => {
  return DYNAMIC_RISK_DIMENSION_NO_DETAIL.includes(key);
};

/**
 * 获取风险维度跳转链接
 * @param key
 * @param item
 * @returns
 * 参考   README_EMBED_PAGES.md
 */
export const getDynamicRiskDimensionURL = (key, item) => {
  let url = '/embed/';
  const str = '';
  const title = item.companyName;
  switch (key) {
    // 行政处罚
    // 金融监管
    case 'AdministrativePenalties':
    case 'FinancialRegulation':
    case 'EnvironmentalProtection':
      url = `/embed/adminpenaltydetail?id=${item.id || item.Id}`;
      break;
    case 'MonitorRestrictedConsumption':
      url = `/embed/sumptuary?id=${item.id || item.Id}&title=${title}`;
      break;
    case 'MonitorInquiryEvaluation':
      url = `/embed/inquiryEvaluation?id=${item.id || item.Id}&title=${title}`;
      break;
    case 'MonitorPublicSecurityNotice':
      url = `/embed/news-detail-page?keyNo=${item.KeyNo}&title=${title}`;
      break;
    case 'MonitorJudicialAuction':
      url = `/embed/judicialSale?id=${item.id || item.Id}&title=${title}`;
      break;
    case 'MonitorProductRecall':
      url = `/embed/recall-product?url=${item.id}&title=${title}`;
      break;
    case 'MonitorAssetsAuction':
      url = `/embed/assetsaleDetail?id=${item.id}&title=${title}`;
      break;
    case 'MonitorBillDispute':
    case 'MonitorUnfairCompetition':
    case 'MonitorCorruptionAndBribery':
    case 'MonitorLoanDispute':
    case 'MonitorTradeDispute':
    case 'MonitorLaborDispute':
    case 'MonitorBankruptcyDispute':
    case 'MonitorImportantDispute':
      url = `/embed/judgementInfo?id=${item.id}&title=${title}`;
      break;
    case 'IPRPledge':
      url = `/embed/${item.isShangbiao ? 'tmDetail' : 'patentDetail'}?id=${item.id}`;
      break;
    default:
      url = `/embed/courtCaseDetail?caseId=${item.CaseSearchId || item.Id || item.id}&title=${title}`;
      break;
  }
  return url;
};

/**
 * 获取风险动态维度跳转方法
 */
export const useDynamicRiskDimensionDetail = () => {
  const vm = getCurrentInstance()?.proxy;

  const openDimensionDetail = (record, ...rest) => {
    const dimensionType = DYNAMIC_RISK_DIMENSION_MAP[record.dimensionKey];
    const isShangbiao = record?.detail?.Content?.includes('出质知产类型：商标');
    if (!hasDynamicRiskDimensionDetailModal(record.dimensionKey)) {
      const url = getDynamicRiskDimensionURL(record.dimensionKey, {
        id: record?.detail?.IPRPledgeId || record.objectId,
        companyName: record.companyName,
        isShangbiao,
      });
      if (url) {
        window.open(url);
      }
      return;
    }

    const params = {
      KeyNo: record.companyId,
    };
    let extra = {};
    switch (record.dimensionKey) {
      case 'MonitorEquityPledge':
        extra = { pledgeId: record.objectId };
        break;
      case 'MonitorTaxCall':
        extra = { objectId: record.objectId, title: '税务催缴详情' };
        break;
      case 'MonitorForeignInvestmentChange':
        extra = { riskId: record.dynamicId, title: get(record, 'detail.Subtitle', undefined) };
        break;
      case 'MonitorTaxReminder':
        extra = { objectId: record.objectId };
        break;
      case 'MonitorEquityFreeze':
      case 'MonitorBankruptcy':
      case 'MonitorDishonestDebtor':
      case 'MonitorPersonExecution':
      case 'MonitorFinalCase':
      case 'MonitorTaxArrearsNotice':
      case 'MonitorBillDefaults':
      case 'MonitorEquityPawn':
      case 'BondDefault':
      case 'MonitorSimpleCancellation':
      case 'MonitorChattelMortgage':
      case 'MonitorMovablePropertySeizure':
        extra = { id: record.objectId };
        break;
      case 'MonitorReductionOfCapital':
        extra = {
          ids: [record.objectId],
          type: 'deceaseCapiNotice',
          keyNo: record.companyId,
        };
        break;
      case 'MonitorBlacklist':
        extra = {
          id: record.objectId,
          Details: record.detail?.OriginalData?.Details,
        };
        break;
      case 'MonitorUpdateRegisteredCapital':
        extra = { riskId: record.dynamicId, title: '注册资本变更' };
        break;
      default:
        extra = { riskId: record.dynamicId };
        break;
    }

    vm?.$modal.showDimension(dimensionType, Object.assign(params, extra), ...rest);
  };

  return [openDimensionDetail];
};
