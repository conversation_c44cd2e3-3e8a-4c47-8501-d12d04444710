import { defineComponent } from 'vue';
import { Divider, Popconfirm, message } from 'ant-design-vue';
import moment from 'moment';
import { useRouter } from 'vue-router/composables';

import RiskAction from '@/shared/components/risk-action';
import { setting } from '@/shared/services';
import QIcon from '@/components/global/q-icon';
import { useMultiSettingStore } from '@/hooks/use-multi-setting-store';
import { createTrackEvent, useTrack } from '@/config/tracking-events';

import styles from './diligence-item.module.less';

const DiligenceItem = defineComponent({
  name: 'DiligenceItem',
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const track = useTrack();
    const router = useRouter();
    const { canCreate, list } = useMultiSettingStore();

    const handleGoUpdate = () => {
      track(createTrackEvent(7721, '准入排查设置', '编辑模型'));
      router.push({
        name: 'investigation-setting-detail',
        params: { type: 'edit' },
        query: { id: props.value.id },
      });
    };
    const handleCopyModel = (ev) => {
      track(createTrackEvent(7721, '准入排查设置', '复制模型'));
      if (!canCreate.value) {
        message.warning('您可使用的模型数量已达上限！');
        ev.stopPropagation();
        return;
      }
      router.push({
        name: 'investigation-setting-detail',
        params: { type: 'copy' },
        query: { id: props.value.id },
      });
    };

    const handleChangeEnabled = () => {
      setting.edit(props.value);
      emit('change', props.value);
    };
    const handleDelete = async () => {
      track(createTrackEvent(7721, '准入排查设置', '删除模型'));
      await setting.remove(props.value.id);
      message.success('删除成功');
      emit('delete', props.value.id);
    };
    return {
      handleGoUpdate,
      handleDelete,
      handleChangeEnabled,
      handleCopyModel,
      canCreate,
      list,
    };
  },
  render() {
    return (
      <div class={[styles.root]} onClick={this.handleGoUpdate}>
        <QIcon class={styles.icon} type="icon-icon_fengxianmoxing1" />
        <div class="flex flex-col flex-1">
          <div class="flex items-center justify-between mb-8px">
            <div class={styles.title}>
              <span class={styles.text}>{this.value?.name || '第三方风险排查标准模型'}</span>
            </div>
            <div class="flex items-center">
              <RiskAction v-permission={[2082]} theme="text" icon="icon-a-bianjigenjin1x" onClick={this.handleGoUpdate}>
                编辑模型
              </RiskAction>
              <Divider type="vertical" v-permission={[2082]} />
              <RiskAction theme="text" icon="icon-gongyingshangchouqu" onClick={this.handleCopyModel}>
                复制模型
              </RiskAction>
              <Divider type="vertical" v-show={this.list.length > 1} />
              <Popconfirm
                title={`您确定要删除该模型吗？`}
                placement="bottomRight"
                onConfirm={this.handleDelete}
                v-show={this.list.length > 1}
              >
                <RiskAction onClick={(e) => e.stopPropagation()} theme="text" icon="icon-icon_yichu">
                  删除模型
                </RiskAction>
              </Popconfirm>
              {/* <Divider type="vertical" />
              <span style={{ marginRight: '4px' }}>启用</span>
              <NumberSwitch size="small" v-model={this.value.enable} onChange={this.handleChangeEnabled} /> */}
            </div>
          </div>

          <div v-show={!!this.value?.description} class="text-#666 text-14px lh-22px mb-8px">
            {this.value?.description}
          </div>

          <div class="text-#999 flex" style={{ gap: '10px' }}>
            <div>
              <span class="text-#999">更新人：</span>
              <span>{this.value?.creator?.name}</span>
            </div>
            <div>
              <span class="text-#999">更新时间：</span>
              <span>{moment(this.value?.createDate).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
          </div>
        </div>
      </div>
    );
  },
});

export default DiligenceItem;
