import { defineComponent } from 'vue';
import { isEmpty, omit, orderBy } from 'lodash';

import RiskSettings from '../risk-settings';
import RiskCreditRate from '../risk-credit-rate';
import RiskMonitorSettings from '../../../cooperative-monitor-setting/modules/risk-dynamics/widgets/risk-settings';
import { createFunctionalEventEmitter } from '@/utils/component';

const RiskBlock = defineComponent({
  functional: true,
  props: {
    /** 风险维度设置 */
    value: {
      type: Object,
      default: () => ({}),
    },
    /** 非正常维度处理: 企查分 */
    extraContent: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isMonitor: {
      type: Boolean,
      default: false,
    },
    // 展示类型
    type: {
      type: String,
    },
  },
  render(h, { props }) {
    const settings = orderBy(Object.entries(omit(props.value, ['version'])), ['1.sort'], ['asc']);
    const hasCreditRate = !isEmpty(props.extraContent?.creditRate);
    const handleUpdateSettingItems = (index: number, value) => {
      settings[index] = value;
    };

    return (
      <div>
        <div>
          {hasCreditRate ? (
            <RiskCreditRate
              disabled={props.disabled}
              value={props.extraContent.creditRate}
              onChange={({ key, status }) => {
                props.extraContent.creditRate[key] = status;
              }}
            />
          ) : null}
        </div>

        {settings.map(([type, setting], index) => {
          if (props.isMonitor) {
            // 合作监控设置 - 风险动态设置
            return (
              <RiskMonitorSettings disabled={props.disabled} key={setting.key} riskKey={setting.key} value={setting} type={props.type} />
            );
          }

          // 准入排查设置
          return <RiskSettings disabled={props.disabled} key={setting.key} riskKey={type} value={setting} type={props.type} />;
        })}
      </div>
    );
  },
});

export default RiskBlock;
