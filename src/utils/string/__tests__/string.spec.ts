import { desensitization, formatChar2HtmlEntity, limitLength } from '..';

describe('formatChar2HtmlEntity', () => {
  test('should replace whitespace with &nbsp;', () => {
    const value = 'hello world';
    const expectedResult = 'hello&nbsp;world';
    expect(formatChar2HtmlEntity(value)).toBe(expectedResult);
  });

  test('should replace line breaks with <br>', () => {
    const value = 'hello\nworld';
    const expectedResult = 'hello<br />world';
    expect(formatChar2HtmlEntity(value)).toBe(expectedResult);
  });

  test('should replace multiple whitespaces with multiple &nbsp;', () => {
    const value = 'hello  world';
    const expectedResult = 'hello&nbsp;&nbsp;world';
    expect(formatChar2HtmlEntity(value)).toBe(expectedResult);
  });

  test('should replace whitespaces and line breaks correctly', () => {
    const value = 'hello  \nworld';
    const expectedResult = 'hello&nbsp;&nbsp;<br />world';
    expect(formatChar2HtmlEntity(value)).toBe(expectedResult);
  });
});

describe('limitLength function', () => {
  test('should return the empty string if no arguments are passed', () => {
    const limited = limitLength()();
    expect(limited).toBe('');
  });

  test('should return the original string if length is less than or equal to the limit', () => {
    const input = 'Hello';
    const limited = limitLength(10)(input);
    expect(limited).toBe(input);
  });

  test('should return a string with max length and trailing dots if input length exceeds the limit', () => {
    const input = 'Hello, World!';
    const limit = 10;
    const expectedOutput = `${input.slice(0, limit)}...`;
    const limited = limitLength(limit)(input);
    expect(limited).toBe(expectedOutput);
  });

  test('should return an empty string if the input is empty', () => {
    const input = '';
    const limited = limitLength(10)(input);
    expect(limited).toBe('');
  });

  test('should handle non-string inputs by converting them to strings', () => {
    const input = 12345;
    const limited = limitLength(10)(input as any);
    expect(limited).toBe(input.toString());
  });
});

describe('desensitization', () => {
  test('desensitization 应该对有效的电子邮件地址进行脱敏', () => {
    expect(desensitization('<EMAIL>')).toBe('t****@example.com');
    expect(desensitization('<EMAIL>')).toBe('tes****<EMAIL>');
    expect(desensitization('<EMAIL>')).toBe('a****@domain.com');
    expect(desensitization('<EMAIL>')).toBe('a****@domain.com');
  });

  test('desensitization 应该对有效的电话号码进行脱敏', () => {
    expect(desensitization('13812345678')).toBe('138****5678');
    expect(desensitization('13545678901')).toBe('135****8901');
    expect(desensitization('123')).toBe('123');
  });

  test('desensitization 应该返回未脱敏的字符串', () => {
    expect(desensitization('invalid email')).toBe('invalid email');
    expect(desensitization('')).toBe('');
    expect(desensitization(null)).toBe(null);
    expect(desensitization(undefined)).toBe(undefined);
  });

  test('边缘情况：特殊字符的处理', () => {
    expect(desensitization('!@#$%^&*()')).toBe('!@#$%^&*()');
  });
});
