import { Tooltip } from 'ant-design-vue';
import { defineComponent, PropType } from 'vue';
import { useRoute } from 'vue-router/composables';

import QIcon from '@/components/global/q-icon';
import RiskDimensionHeading from '@/shared/components/risk-dimension-heading';
import { getTranslateDetail } from '@/shared/services/diligence.service';
import { createFunctionalEventEmitter } from '@/utils/component';
import DiligenceEmpty from '@/shared/components/diligence-empty';

import { DIMENSIONDESCMAP, DIMENSIONS_TABLE_CONFIG } from '../../bidding-investigation/config';
import RelationsDimension from '../../bidding-investigation/widgets/relations-dimension';
import TableBlock from '../../bidding-investigation/widgets/table-block';
import styles from '../bidding-investigation-detail.module.less';
import { DimensionHitsDetail } from './tender-dimension-block';

const TenderDimensionBlockContent = defineComponent({
  functional: true,
  props: {
    dimensionHitsDetails: {
      type: Array as PropType<DimensionHitsDetail[]>,
      default: () => [],
    },
    dataSource: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  },
  render(h, { props, listeners }) {
    const emitters = createFunctionalEventEmitter(listeners);
    const route = useRoute();

    // 过滤空数据
    const dimensions = props.dimensionHitsDetails.filter(({ key, subDimension = [] }) => {
      if (key === 'BiddingCompanyRelation' && !subDimension?.length) {
        return false;
      }
      return true;
    });

    if (!dimensions?.length || dimensions.length === 0) {
      return null;
    }

    const renderBiddingCompanyRelation = (key, subDimension) => {
      return (
        <RelationsDimension key={key} dataSource={props.dataSource} data={subDimension} id={key}>
          <DiligenceEmpty slot="empty" />
        </RelationsDimension>
      );
    };
    const dimensionDetailsRenderer = (dimensionHitsDetails: DimensionHitsDetail[]) => {
      return dimensionHitsDetails.map(({ key, name, data = [], subDimension = [], description = '', totalHits }: DimensionHitsDetail) => {
        // 渲染维度模块
        const table = DIMENSIONS_TABLE_CONFIG[key];
        // 公司 KeyNo 与名称
        const keyNoAndNames = props.dataSource.companyList?.map((item) => ({
          companyId: item.companyId,
          companyName: item.companyName,
          KeyNo: item.companyId,
        }));
        // 表格点击事件
        const handleTableClick = () => {
          // this.$track(createTrackEvent(6933, '招标排查详情', name));
          emitters('click')({
            key,
            name,
          });
        };
        // 描述逻辑
        const descriptionText = DIMENSIONDESCMAP[key] || description;

        const renderTable = () => {
          if (!subDimension?.length && table && totalHits) {
            return (
              <TableBlock
                meta={table}
                params={{
                  ...table.params,
                  key: table.key,
                  diligenceId: +route.query.id,
                  keyNoAndNames,
                }}
                request={table.request}
                columns={table.columns}
                defaultData={data}
                nativeOnClick={handleTableClick}
              />
            );
          }
          if (subDimension?.length) {
            const dimensionTable = subDimension.filter((item) => item.data);
            if (!dimensionTable.length) {
              return <DiligenceEmpty />;
            }
            // 渲染关联关系
            if (key === 'BiddingCompanyRelation') {
              return renderBiddingCompanyRelation(key, subDimension);
            }
            return dimensionTable.map((subTable) => {
              const tableConfig = DIMENSIONS_TABLE_CONFIG[subTable.key];
              if (!tableConfig) {
                console.warn('该维度无法正常显示', subTable);
              }

              const defaultData = getTranslateDetail(subTable.data);
              return (
                <div key={subTable.key} class={styles.subSection}>
                  <header class={styles.header}>
                    <RiskDimensionHeading content={subTable?.description || subTable.name} showTag={false} />
                    <span
                      style={{
                        marginLeft: '4px',
                        color: '#F04040',
                        fontWeight: 'bold',
                      }}
                    >
                      {subTable.totalHits}
                    </span>
                  </header>

                  {tableConfig ? (
                    // 表格数据
                    <TableBlock
                      rowKey={tableConfig?.rowKey ?? 'id'}
                      meta={tableConfig}
                      params={{
                        ...tableConfig.params,
                        key: tableConfig.key,
                        keyNoAndNames,
                      }}
                      request={tableConfig.request}
                      columns={tableConfig.columns}
                      defaultData={defaultData}
                      nativeOnClick={handleTableClick}
                    />
                  ) : null}
                </div>
              );
            });
          }
          return <DiligenceEmpty />;
        };

        return (
          <div class={styles.section} key={key} id={key}>
            <header class={styles.header}>
              <div class={styles.title}>
                <span>{name}</span>
                <em>{totalHits}</em>
                {descriptionText ? (
                  <Tooltip>
                    <div slot="title" domPropsInnerHTML={descriptionText}></div>
                    <QIcon style={{ fontSize: '14px', cursor: 'pointer' }} type="icon-zhushi" />
                  </Tooltip>
                ) : null}
              </div>
            </header>
            <div class={styles.body}>
              {/* 共同投标分析、资质筛查详情表格 */}
              {renderTable()}
            </div>
          </div>
        );
      });
    };

    return <div>{dimensionDetailsRenderer(dimensions)}</div>;
  },
});

export default TenderDimensionBlockContent;
