import { shallowMount } from '@vue/test-utils';

import RiskTrendsTab from '@/components/tabs';

import AnnualReviewRecord from '..';
import { batchImport } from '@/shared/services';

const mockRoute = {
  name: 'ROUTE_NAME',
  query: {},
};
const mockRouter = {
  route: mockRoute,
  push: vi.fn(),
  replace: vi.fn(),
};
vi.mock('vue-router/composables', () => ({
  useRouter: () => mockRouter,
  useRoute: () => mockRoute,
}));
vi.mock('@/shared/services');

describe('AnnualReviewRecord', () => {
  beforeEach(() => {
    vi.mocked(batchImport).search.mockResolvedValue({
      data: [],
    });
  });
  afterEach(() => {
    vi.resetAllMocks();
  });

  test('render', () => {
    const wrapper = shallowMount<InstanceType<typeof AnnualReviewRecord>>(AnnualReviewRecord);
    expect(wrapper).toMatchSnapshot();
  });

  test('actions: handleTabChange', () => {
    const wrapper = shallowMount<InstanceType<typeof AnnualReviewRecord>>(AnnualReviewRecord);
    wrapper.findComponent(RiskTrendsTab).vm.$emit('change', 'ROUTE_NAME');
    expect(mockRouter.push).toHaveBeenCalledWith({
      name: 'ROUTE_NAME',
      query: {
        useCacheQuery: 'true',
      },
    });
  });
});
