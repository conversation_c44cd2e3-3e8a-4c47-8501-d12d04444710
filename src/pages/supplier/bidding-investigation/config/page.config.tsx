import { isEmpty } from 'lodash';
import { VNode } from 'vue';

import { biddings as biddingsService } from '@/shared/services';
import { createChartData } from '@/shared/charts/relationship-chart/helper/create-chart-data';
import { Group } from '@/components/global/q-filter/interface';
import { DEFAULT_DATE_RANGE } from '@/config/tender.config';
import BidBatch from '@/assets/images/bid-batch.png';
import BidGuide from '@/assets/images/bid-guide.png';
import { TenderResultList } from '@/shared/config/bidding-investigation-detail.config';
import { riskColumns } from '@/shared/config/investigate-detail-risk-columns.config';

import {
  GovernmentPurchaseIllegalColumns,
  GovProcurementIllegalColumns,
  AdministrativePenalties2Columns,
  ArmyProcurementIllegalColumns,
  CaseColumns,
  IntPatentColumns,
  PatentColumns,
  SoftwareCopyrightColumns,
  GuarantorColumns,
  EquityPledgeColumns,
  JointBidIColumns,
  CompanyBranchColumns,
  QualificationCertificateColumns,
  BidCollusiveListColumns,
  DirectConnectionColumns,
  BlackListInvestigationsColumns,
  CompanyCreditColumns,
  TaxationOffencesColumns,
  PersonCreditCurrentColumns,
  FakeSOESColumns,
} from './dimension-columns.config';
import { openRelationalChartDialog } from '../../../../shared/composables/open-dialog-drawer';

export const TABS = [
  { label: '招标排查', value: 'bidding-investigation', permission: [2110] },
  { label: '批量招标排查', value: 'bidding-batchTask', permission: [2114] },
];

export const DIMENSIONDESCMAP = {
  PurchaseIllegal:
    '通过智能核查供应商历史不良记录，覆盖：司法/处罚（围串标诉讼/处罚）、采购黑名单（国央企/军队）、信用风险（失信/税收违法/假冒国企），有效拦截高风险供应商，保障采购合规性。',
  JointBiddingAnalysis:
    '招标排查企业之间共同参与投标项目结果的聚合统计分析。仅供用户参考，该结果并不代表企查查的任何明示、暗示之观点或保证。',
};

export const DILIGENCE_SEARCH_FILTER_GROUPS: Group[] = [
  {
    field: 'filters',
    label: '筛选条件',
    type: 'groups',
    children: [
      {
        field: 'status',
        type: 'multiple',
        label: '排查结果',
        options: TenderResultList,
        layout: 'inline',
      },
      {
        field: 'operators',
        type: 'multiple',
        label: '操作人',
        options: [],
        layout: 'inline',
        meta: {
          showFilter: true,
        },
      },
      {
        field: 'sd',
        type: 'single',
        label: '排查时间',
        options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
        custom: {
          type: 'date-range',
        },
      },
    ],
  },
];

/**
 * FIXME: 重复代码 chart edges
 */
export const renderAtlasChart = (text, record) => {
  return (
    <a
      onClick={() => {
        const data = createChartData(record);
        openRelationalChartDialog(data);
      }}
    >
      图谱
    </a>
  );
};

export const DIMENSIONS_TABLE_CONFIG = {
  ...Object.entries(riskColumns).reduce((acc, [key, columns]) => {
    acc[key] = {
      key,
      columns,
      request: biddingsService.getRelationListByKey,
    };
    return acc;
  }, {}),
  JointBiddingAnalysis: {
    key: 'JointBiddingAnalysis',
    request: biddingsService.getJointBiddingAnalysis,
    columns: JointBidIColumns,
  },
  HitInnerBlackList: {
    key: 'HitInnerBlackList',
    request: biddingsService.getHitInnerBlackList,
    columns: riskColumns.HitInnerBlackList, // 被列入内部黑名单
  },
  ForeignInvestment: {
    key: 'ForeignInvestment',
    request: biddingsService.getForeignInvestment,
    columns: [
      {
        title: '被排查企业',
        width: 260,
        scopedSlots: { customRender: 'companyName' },
      },
      ...riskColumns.ForeignInvestment.slice(0, -1),
      {
        title: '图谱',
        width: 78,
        customRender: renderAtlasChart,
      },
    ], // 被列入内部黑名单
  },
  Shareholder: {
    key: 'Shareholder',
    request: biddingsService.getShareholder,
    columns: [
      {
        title: '被排查企业',
        width: 260,
        scopedSlots: { customRender: 'companyName' },
      },
      ...riskColumns.Shareholder.slice(0, -1),
      {
        title: '图谱',
        width: 78,
        customRender: renderAtlasChart,
      },
    ], // 参股股东被列入内部黑名单
  },
  EmploymentRelationship: {
    key: 'EmploymentRelationship',
    request: biddingsService.getEmploymentRelationship,
    columns: [
      {
        title: '被排查企业',
        width: 260,
        scopedSlots: { customRender: 'companyName' },
      },
      ...riskColumns.EmploymentRelationship.slice(0, -1),
      {
        title: '图谱',
        width: 78,
        customRender: renderAtlasChart,
      },
    ], // 与内部黑名单列表存在人员关联
  },
  BidAdministrativeJudgement: {
    rowKey: 'Id',
    key: 'BidAdministrativeJudgement',
    request: biddingsService.getRelationListByKey,
    columns: [
      {
        title: '案号',
        dataIndex: 'caseno',
        customRender: (text) => <div domPropsInnerHTML={text.replaceAll('\n', '<br>')}></div>,
      },
      {
        title: '案由',
        width: 100,
        dataIndex: 'casereason',
      },
      {
        title: '当事人',
        scopedSlots: { customRender: 'Parties' },
      },
      {
        title: '案件金额(元) ',
        width: 120,
        dataIndex: 'amountinvolved',
        scopedSlots: { customRender: 'money' },
      },
      {
        title: '发布日期',
        width: 100,
        dataIndex: 'submitdate',
        scopedSlots: { customRender: 'date' },
        sorter: true,
        key: 'courtdate',
      },
      {
        title: '内容',
        width: 70,
        scopedSlots: { customRender: 'urlAction' },
      },
    ],
  },
  BidAdministrativePenalties: {
    key: 'BidAdministrativePenalties',
    request: biddingsService.getBidAdministrativePenalties,
    columns: AdministrativePenalties2Columns,
  },
  governmentPurchaseIllegal: {
    key: 'governmentPurchaseIllegal',
    request: biddingsService.getGovBlackList,
    columns: GovernmentPurchaseIllegalColumns,
  },
  GovProcurementIllegal: {
    rowKey: 'Id',
    key: 'GovProcurementIllegal',
    request: biddingsService.getGovProcurementIllegal,
    columns: GovProcurementIllegalColumns,
  },
  BlacklistSameSuspectedActualController: {
    key: 'BlacklistSameSuspectedActualController',
    request: biddingsService.getRelationListByKey,
    columns: [
      {
        title: '被排查企业',
        width: 260,
        customRender: (item) => {
          return (
            <a href={`/embed/companyDetail?keyNo=${item.sourceCompanyId}&title=${item.sourceCompanyName}`} target="_blank">
              {item.sourceCompanyName}
            </a>
          );
        },
      },
      ...riskColumns.BlacklistSameSuspectedActualController,
    ],
  },
  StaffWorkingOutside: {
    key: 'StaffWorkingOutside',
    request: biddingsService.getRelationListByKey,
    columns: [
      {
        title: '被排查企业',
        width: 260,
        customRender: (item) => {
          return (
            <a href={`/embed/companyDetail?keyNo=${item.sourceCompanyId}&title=${item.sourceCompanyName}`} target="_blank">
              {item.sourceCompanyName}
            </a>
          );
        },
      },
      ...riskColumns.StaffWorkingOutside.slice(0, -1),
    ],
  },
  StaffWorkingOutsideForeignInvestment: {
    key: 'StaffWorkingOutsideForeignInvestment',
    request: biddingsService.getRelationListByKey,
    columns: [
      {
        title: '被排查企业',
        width: 260,
        customRender: (item) => {
          return (
            <a href={`/embed/companyDetail?keyNo=${item.sourceCompanyId}&title=${item.sourceCompanyName}`} target="_blank">
              {item.sourceCompanyName}
            </a>
          );
        },
      },
      ...riskColumns.StaffWorkingOutside.slice(0, -1),
    ],
  },
  SuspectedInterestConflict: {
    key: 'SuspectedInterestConflict',
    request: biddingsService.getRelationListByKey,
    columns: [
      {
        title: '疑似关系',
        dataIndex: 'dimension',
        width: 400,
        customRender: (text, item) => {
          const nodes: VNode[] = [];

          if (item.isSameName) {
            const name = item.name || item.Name;
            const keyNo = item.keyNo || item.KeyNo || '';
            // 需要判断是否为企业
            const isPerson = keyNo.startsWith('p');
            const url = isPerson
              ? `/embed/beneficaryDetail?personId=${keyNo}&title=${name}`
              : `/embed/companyDetail?keyNo=${keyNo}&title=${name}`;

            const node = keyNo ? (
              <a href={url} target="_blank">
                {name}
              </a>
            ) : (
              <span>{name}</span>
            );
            nodes.push(
              <div>
                <span>疑似同名</span>
                <span>（{node}）</span>
              </div>
            );
          }

          // 相同联系方式
          if (item.isSameContact) {
            const contacts: string[] = [];
            if (!isEmpty(item.phones)) {
              const phones = item.phones.map(({ n, t }) => `${n} ${t}`);
              contacts.push(...phones);
            }
            if (!isEmpty(item.emails)) {
              const emails = item.emails.map(({ e }) => e);
              contacts.push(...emails);
            }

            if (!isEmpty(contacts)) {
              nodes.push(
                <div>
                  <span>相同联系方式</span>
                  <span>（{contacts.join('，')}）</span>
                </div>
              );
            }
          }
          return <div>{nodes}</div>;
        },
      },
      {
        title: '被排查企业',
        width: 280,
        customRender: (item) => {
          return (
            <a href={`/embed/companyDetail?keyNo=${item.sourceCompanyId}&title=${item.sourceCompanyName}`} target="_blank">
              {item.sourceCompanyName}
            </a>
          );
        },
      },
      {
        title: '人员类型/职务',
        dataIndex: 'job',
      },
      {
        title: '“疑似潜在利冲”人员',
        scopedSlots: { customRender: 'PersonNo' },
      },

      {
        title: '“疑似潜在利冲”人员分组',
        dataIndex: 'group',
      },

      {
        title: '操作',
        scopedSlots: { customRender: 'checkStatus' },
      },
    ],
  },
  StaffForeignInvestment: {
    key: 'StaffForeignInvestment',
    request: biddingsService.getRelationListByKey,
    columns: [
      {
        title: '被排查企业',
        width: 260,
        customRender: (item) => {
          return (
            <a href={`/embed/companyDetail?keyNo=${item.sourceCompanyId}&title=${item.sourceCompanyName}`} target="_blank">
              {item.sourceCompanyName}
            </a>
          );
        },
      },
      ...riskColumns.StaffForeignInvestment.slice(0, -1),
    ],
  },
  SamePhone: {
    key: 'SamePhone',
    request: biddingsService.getRelationListByKey,
    columns: [
      {
        title: '被排查企业',
        width: 260,
        customRender: (item) => {
          return (
            <a href={`/embed/companyDetail?keyNo=${item.sourceCompanyId}&title=${item.sourceCompanyName}`} target="_blank">
              {item.sourceCompanyName}
            </a>
          );
        },
      },
      ...riskColumns.SamePhone,
    ],
  },
  Guarantor: {
    key: 'Guarantor',
    request: biddingsService.getGarantorList,
    columns: GuarantorColumns,
  },
  EquityPledge: {
    key: 'EquityPledge',
    request: biddingsService.getEquityPledge,
    columns: GuarantorColumns,
  },
  ArmyProcurementIllegal: {
    key: 'ArmyProcurementIllegal',
    title: '军队采购失信名单',
    request: biddingsService.getArmyProcurementIllegal,
    columns: ArmyProcurementIllegalColumns,
  },
  CompanyBranch: {
    key: 'CompanyBranch',
    title: '支机构排查名单',
    request: biddingsService.getArmyProcurementIllegal,
    columns: CompanyBranchColumns,
  },
  BiddingCompanyCertification: {
    key: 'BiddingCompanyCertification',
    title: '资质筛查',
    request: biddingsService.getBiddingCompanyCertification,
    columns: QualificationCertificateColumns,
  },
  DirectConnection: {
    key: 'DirectConnection',
    rowKey: 'blacklistId',
    title: '被列入内部黑名单',
    request: biddingsService.getDirectConnection,
    columns: DirectConnectionColumns,
  },
  BlackListInvestigations: {
    key: 'BlackListInvestigations',
    title: '内部黑名单关联关系',
    request: biddingsService.getBlackListInvestigations,
    columns: BlackListInvestigationsColumns,
  },
  CompanyCredit: {
    key: 'CompanyCredit',
    request: biddingsService.getRelationListByKey,
    columns: CompanyCreditColumns,
  },
  PersonCreditCurrent: {
    key: 'PersonCreditCurrent',
    request: biddingsService.getRelationListByKey,
    columns: PersonCreditCurrentColumns,
  },
  TaxationOffences: {
    key: 'TaxationOffences',
    request: biddingsService.getRelationListByKey,
    columns: TaxationOffencesColumns,
  },
  FakeSOES: {
    key: 'FakeSOES',
    request: biddingsService.getRelationListByKey,
    columns: FakeSOESColumns,
  },
};

export const TableMap = {
  Case: {
    title: '相同司法案件信息',
    columns: () => CaseColumns,
  },
  Patent: {
    title: '相同专利信息',
    columns: () => PatentColumns,
  },
  IntPatent: {
    title: '相同国际专利信息',
    columns: () => IntPatentColumns,
  },
  SoftwareCopyright: {
    title: '相同软件著作权',
    columns: () => SoftwareCopyrightColumns,
  },
  Guarantor: {
    title: '相互担保关联',
    columns: () => GuarantorColumns,
  },
  EquityPledge: {
    title: '股权出质关联',
    columns: () => EquityPledgeColumns,
  },
  BidCollusive: {
    title: '围串标关联信息',
    columns: () => BidCollusiveListColumns,
  },
  ChattelMortgage: {
    title: '动产抵押关联',
    columns: () => riskColumns.ChattelMortgage,
  },
  ControlRelation: {
    title: '控制关系',
    columns: () => [],
  },
};

export const SINGLE_GUIDE_DATA = {
  img: BidGuide,
  desText: [
    ' 1、招标排查采用先进的大数据处理技术，挖掘供应商之间的股权关联、对外投资关系、人员任职关系、疑似关联关系，识别潜在围标、串标可能性，协助企业维护招标采购公平公正环境，预防围标串标，有效控制外部合作风险；',
    ' 2、招标排查，是针对上传的企业名录进行以下检查：a.是否存在关联关系；b.历史投标记录是否存在异常；c.是否与内部黑名单存在关联关系（一层）；d.是否存在围标串标处罚；e.是否被列入涉采购黑名单；',
    ' 3、招标排查，最多同时揭示&nbsp;<em>20</em>&nbsp;家企业关联关系。',
  ],
};

export const BATCH_GUIDE_DATA = {
  img: BidBatch,
  desText: [
    '1、招标排查采用先进的大数据处理技术，挖掘供应商之间的股权关联、对外投资关系、人员任职关系、疑似关联关系，识别潜在围标、串标可能性，协助企业维护招标采购公平公正环境，预防围标串标，有效控制外部合作风险；',
    '2、批量招标排查，是针对上传项目对应的企业名录进行以下检查:a.是否存在关联关系;b.历史投标记录是否存在异常;c.是否与内部黑名单存在关联关系(一层);d.是否存在围标串标处罚;e.是否被列入涉采购黑名单;',
    '3、批量招标排查，最多同时支持&nbsp;<em>1000</em>&nbsp;个项目进行招标排查，单个项目排查企业数不超过&nbsp;<em>20</em>&nbsp;家。',
  ],
};
