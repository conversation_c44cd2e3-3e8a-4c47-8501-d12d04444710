import { computed, defineComponent, nextTick, ref, unref } from 'vue';
import { Button, FormModel, Input, Tooltip } from 'ant-design-vue';
import { isNil } from 'lodash';

import QIcon from '@/components/global/q-icon';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { SuggestionsList, TenderResultMap, TenderSuggestionMap } from '@/shared/config/bidding-investigation-detail.config';
import { openTenderRemarkDrawer } from '@/shared/components/tender-remark-history';
import { UploadFileNew } from '@/components/upload-file-new';

import IconChecked from '../../../icon-checked';
import styles from './tender-result-advice.module.less';
import TenderResultAnalysisBlock from '../../../tender-result-analysis-block';

const TenderResultAdvice = defineComponent({
  name: 'TenderResultAdvice',
  props: {
    dataSource: {
      type: Object,
      required: false,
    },
    /**
     * 是否隐藏人工复核操作
     */
    hideReviewAction: {
      type: Boolean,
      default: false,
    },
    saveFn: {
      type: Function,
      required: true,
    },
    /**
     * 是否显示风险简析
     */
    showRiskAnalysis: {
      type: Boolean,
      default: true,
    },
  },
  setup(props, { emit }) {
    const track = useTrack();
    const textareaRef = ref<HTMLElement>();
    const form = ref({
      status: -1,
      comment: '',
      attachments: [],
    });
    const isExpand = ref(false);
    const currentRemark = computed(() => props.dataSource?.remark?.[0]);

    const hasRemark = computed(() => {
      return !isNil(unref(currentRemark.value?.status));
    });
    const handleSubmit = async () => {
      await props.saveFn({
        id: props.dataSource?.id,
        ...unref(form),
      });
      isExpand.value = false;
      emit('search');
      track(createTrackEvent(6933, '招标排查详情', '保存排查意见'));
    };
    const init = () => {
      if (hasRemark.value) {
        form.value.status = unref(currentRemark.value?.status) ?? -1;
        form.value.comment = unref(currentRemark)?.details?.comment || '';
        form.value.attachments = unref(currentRemark)?.details?.attachments || [];
      }
    };
    const suggestionText = computed(() => {
      return props.dataSource?.description || '无建议';
    });

    const handleOpenRemarkDrawer = (data = {}) => {
      openTenderRemarkDrawer({
        title: `排查记录 - ${props.dataSource?.projectName}（${props.dataSource?.projectNo}）`,
        data,
      });
    };

    const visible = ref(false);

    return {
      form,
      isExpand,
      handleSubmit,
      hasRemark,
      init,
      visible,
      suggestionText,

      textareaRef,
      handleOpenRemarkDrawer,

      currentRemark,
    };
  },
  render() {
    const renderForm = () => {
      return [
        <div style={{ margin: '10px 0' }} v-show={this.isExpand} class={styles.hr}></div>,
        <FormModel
          v-show={this.isExpand}
          props={{ model: this.form }}
          label-col={{ span: 3 }}
          colon={false}
          wrapper-col={{ span: 21 }}
          labelAlign="left"
        >
          <FormModel.Item label="排查意见">
            <div class={styles.radioBtnWrapper}>
              {SuggestionsList.map((item) => {
                return (
                  <div
                    key={item.value}
                    onClick={() => {
                      this.form.status = item.value;
                    }}
                    class={[
                      {
                        [styles.radioBtn]: true,
                      },
                      'relative text-12px border-1',
                    ]}
                    style={{
                      ...TenderSuggestionMap[item.value]?.style,
                    }}
                  >
                    <span>{item.label}</span>
                    <div v-show={item.value === this.form.status} class="flex absolute right-0 bottom-0">
                      <IconChecked />
                    </div>
                  </div>
                );
              })}
            </div>
          </FormModel.Item>
          <FormModel.Item label="备注">
            <div class="relative">
              <Input.TextArea
                ref="textareaRef"
                style={{ minHeight: '100px', padding: '8px 10px' }}
                placeholder="请输入您的备注信息"
                maxLength={200}
                v-model={this.form.comment}
              ></Input.TextArea>
              <div class="text-14px text-#999 absolute" style={{ right: '8px', bottom: '3px' }}>
                <span style={{ color: this.form.comment?.length === 200 ? '#F04040' : 'inherit' }}>{this.form.comment?.length}</span>
                {` / `}
                <span>{200}</span>
              </div>
            </div>
          </FormModel.Item>
          <FormModel.Item>
            <div style={{ marginTop: '5px' }} slot="label">
              附件
            </div>
            <UploadFileNew v-model={this.form.attachments} />
          </FormModel.Item>
          <FormModel.Item label=" " class="text-right">
            <div class="flex gap-10px justify-end">
              <Button
                onClick={() => {
                  this.isExpand = false;
                }}
              >
                取消
              </Button>
              <Button type="primary" onClick={this.handleSubmit}>
                保存结果
              </Button>
            </div>
          </FormModel.Item>
        </FormModel>,
      ];
    };

    return (
      <div
        class={styles.container}
        style={{
          padding: this.isExpand ? '10px 15px 15px' : '10px 15px',
        }}
      >
        <div class="flex items-center" style={{ gap: '30px' }}>
          <div v-show={!this.hasRemark || this.isExpand} class="flex items-center" style={{ flex: '1', gap: '30px', minHeight: '32px' }}>
            <div class={styles.remarkLabel}>
              <span style={{ color: '#333' }}>
                排查建议
                <Tooltip placement="rightTop">
                  <div slot="title">
                    排查建议依据用户根据自身实际业务需求所设置的排查模型而生成，以辅助业务决策判断，并不代表企查查方的任何明示、暗示之观点或保证。
                  </div>
                  <QIcon style={{ color: '#d8d8d8', cursor: 'pointer', marginLeft: '5px' }} type="icon-zhushi" />
                </Tooltip>
                ：
              </span>
              <span style={{ color: TenderResultMap[this.dataSource?.result]?.style?.color }}>
                {TenderResultMap[this.dataSource?.result]?.label || '-'}
              </span>
            </div>
            <div style={{ flex: '1', color: '#666' }} domPropsInnerHTML={this.suggestionText}></div>
          </div>
          <div v-show={this.hasRemark && !this.isExpand} class="flex items-center" style={{ flex: '1', gap: '30px', minHeight: '32px' }}>
            <div class={styles.remarkLabel}>
              <span style={{ color: '#333' }}>排查意见：</span>
              <span class={styles.remarkText} style={{ color: TenderSuggestionMap[this.currentRemark?.status]?.style?.color }}>
                <span> {TenderSuggestionMap[this.currentRemark?.status]?.label || '-'}</span>

                {this.dataSource?.remark?.length ? (
                  <Tooltip
                    v-model={this.visible}
                    placement="bottomLeft"
                    overlayClassName={styles.tooltip}
                    trigger="hover"
                    title="查看更多记录"
                  >
                    <QIcon
                      type="icon-shenhe"
                      class={[styles.remarkIcon, this.visible ? styles.remarkIconLight : '']}
                      onClick={() => this.handleOpenRemarkDrawer(this.dataSource?.remark)}
                    />
                  </Tooltip>
                ) : null}
              </span>
            </div>
            <div class={styles.remarkCommet}>{this.dataSource?.remark?.[0]?.details?.comment || '-'}</div>
          </div>
          <Button
            data-capture="exclude"
            type="primary"
            v-show={!this.isExpand && !this.hideReviewAction}
            onClick={async () => {
              this.$track(createTrackEvent(6933, '招标排查详情', '人工复核'));
              this.isExpand = true;
              await nextTick();
              this.textareaRef?.focus();
              this.init();
            }}
          >
            <QIcon type="icon-a-bianjixian1x"></QIcon>
            人工复核
          </Button>
        </div>

        <div v-show={!this.isExpand && this.hasRemark} class="flex" style={{ gap: '30px', color: '#999', marginTop: '10px' }}>
          <div style={{ width: '156px', flexShrink: '0' }}>排查建议：{TenderResultMap[this.dataSource?.result]?.label}</div>
          <div domPropsInnerHTML={this.suggestionText}></div>
        </div>
        {this.showRiskAnalysis ? <TenderResultAnalysisBlock dataSource={this.dataSource} /> : null}

        {renderForm()}
      </div>
    );
  },
});

export default TenderResultAdvice;
