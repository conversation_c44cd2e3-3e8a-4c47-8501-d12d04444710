import { defineComponent, nextTick, PropType, ref, VNode, computed } from 'vue';
import { cloneDeep, isEmpty, pick } from 'lodash';
import { message, Button, Dropdown } from 'ant-design-vue';

import QRichTable from '@/components/global/q-rich-table';
import DiligenceEmpty from '@/shared/components/diligence-empty';
import QEntityLink from '@/components/global/q-entity-link';
import { person as personService, potential as potentialService } from '@/shared/services';
import { openCheckSyncModal } from '@/components/modal/check-sync';
import { openStaffCheckModal } from '@/components/modal/staff-check';
import { PotentialPermissionCode } from '@/shared/constants/diligence.constant';
import QIcon from '@/components/global/q-icon';
import QCard from '@/components/global/q-card';
import QCompanyStatus from '@/components/global/q-company-status';

import RelationsContent from '../relations-content';
import dimensionColumns from './potential-columns.config';
import styles from './risk-content.module.less';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import RelationshipChart from '@/pages/supplier/bidding-investigation/widgets/relational-chart';
import { parseGraphData } from '@/shared/components/_helpers/data-parser';

// 抽取的风险维度卡片组件
const RiskDimensionCard = defineComponent({
  name: 'RiskDimensionCard',
  props: {
    dimensionKey: {
      type: String,
      required: true,
    },
    dataSource: {
      type: Array,
      required: true,
    },
    hideAction: {
      type: Boolean,
      default: false,
    },
    companyInfo: {
      type: Object as PropType<{ keyNo: string; name: string }>,
      default: () => ({}),
    },
    snapshotId: {
      type: String,
      default: '',
    },
    id: {
      type: Number,
      required: true,
    },
  },
  emits: ['check'],
  setup(props, { emit }) {
    const track = useTrack();

    // 计算关系链图表数据
    const relationPathGraphData = computed(() => {
      const relationPathList = props.dataSource.flatMap((item: any) => item?.companyPerson?.relationPathList || []);
      return parseGraphData(relationPathList);
    });

    // 计算表格列配置
    const tableColumns = computed(() => {
      let columns = cloneDeep(dimensionColumns[props.dimensionKey].columns);
      if (props.hideAction) {
        columns = columns.slice(0, -1);
      }
      return columns;
    });

    // 维度配置信息
    const dimensionConfig = computed(() => dimensionColumns[props.dimensionKey]);

    // 核实
    const onCheck = (key, item) => {
      emit('check', key, item);
    };

    return {
      relationPathGraphData,
      tableColumns,
      dimensionConfig,
      track,
      onCheck,
    };
  },
  render() {
    const { dimensionConfig, tableColumns, dataSource, relationPathGraphData } = this;
    const { name, icon, color } = dimensionConfig;

    return (
      <QCard bodyStyle={{ padding: '15px' }}>
        <div slot="title">
          {name}
          <QIcon type={icon} style={{ color, marginLeft: '6px' }} />
        </div>

        <QRichTable
          rowKey="id"
          headerAlign={'left'}
          columns={tableColumns}
          dataSource={dataSource}
          showIndex
          scopedSlots={{
            CompanyName: (record) => {
              const data = record.companyPerson;
              if (!data) return '-';
              return <QEntityLink class={styles.inline} coyObj={{ KeyNo: data.companyId, Name: data.companyName }} />;
            },
            PersonName: (record) => {
              const data = record.companyPerson;
              const personData = record.person || {};
              if (!data) return '-';
              const KeyNo = data.keyNo;
              const Name = data.name;
              return (
                <div class="flex items-center" style={{ gap: '4px' }}>
                  <QEntityLink coyObj={{ KeyNo, Name }} />
                  {personData.relationPersonId === -1 ? (
                    <div class={[styles.tag, styles.orange]}>本人</div>
                  ) : (
                    <div class={[styles.tag, styles.cyan]}>近亲属</div>
                  )}
                </div>
              );
            },
            interestConflictPerson: (record) => {
              const nodes: VNode[] = [];
              const { isSameName, isSameContact } = record.person;

              // 相同姓名
              if (isSameName) {
                const personName = record.companyPerson.name;
                const keyNo = record.companyPerson.keyNo ?? '';
                const node = keyNo ? <QEntityLink coyObj={{ KeyNo: keyNo, Name: personName }} /> : <span>{personName}</span>;
                nodes.push(
                  <div>
                    <span>疑似同名</span>
                    <span>（{node}）</span>
                  </div>
                );
              }

              // 相同联系方式
              if (isSameContact) {
                const contacts: string[] = [];
                if (!isEmpty(record.person.samePhone)) {
                  contacts.push(record.person.samePhone);
                }
                if (!isEmpty(record.person.sameEmail)) {
                  contacts.push(record.person.sameEmail);
                }

                if (!isEmpty(contacts)) {
                  nodes.push(
                    <div>
                      <span>相同联系方式</span>
                      <span>（{contacts.join('，')}）</span>
                    </div>
                  );
                }
              }

              return <div>{nodes}</div>;
            },
            ActionCheck: (item) => {
              const { person = {}, companyPerson = {} } = item;
              if (person.status === 1) {
                return <span style={{ color: '#999' }}>已核实</span>;
              }

              // 没有 keyNo 不渲染
              if (!companyPerson.keyNo) {
                return '-';
              }

              return (
                <a v-permission={[PotentialPermissionCode.identityVerify]} onClick={() => this.onCheck(this.dimensionKey, item)}>
                  核实
                </a>
              );
            },
            ShortStatus: (status) => {
              if (!status) return '-';
              return <QCompanyStatus class="q-company-status__root qcStatus" status={status} ghost />;
            },
            RelationPath: (record) => {
              const pathList = record.companyPerson.relationPathList || [];
              if (!pathList.length) {
                return '-';
              }
              const pathData = pathList.flatMap((item) => item.relations);
              return (
                <Dropdown
                  placement="bottomRight"
                  overlayClassName={styles.relationOverlay}
                  onVisibleChange={(visible) => {
                    if (visible) {
                      this.track(createTrackEvent(8893, '潜在利益关系排查详情', '查看关系链'));
                    }
                  }}
                >
                  <RelationsContent slot="overlay" data={pathData} namespace={this.dimensionKey} />
                  <Button type="link" class={styles.relationButton}>
                    <span>查看详情</span>
                    <QIcon type="icon-a-shixinxia1x1" />
                  </Button>
                </Dropdown>
              );
            },
            StaffName: (record) => {
              const data = record.person;
              if (!data) return '-';
              const { relationPersonId, relationPersonName, relationPersonKeyNo, relationship, name: staffName, personNo } = data;
              if (relationPersonId && relationPersonId !== -1) {
                const relationPersonNo = personNo.split(`_${relationship}_`)[0];
                return (
                  <div>
                    <span>
                      {relationPersonNo}_
                      <QEntityLink coyObj={{ KeyNo: relationPersonKeyNo, Name: relationPersonName }} ellipsis={false} />
                    </span>
                    <span>—{staffName}</span>
                    <span>（{relationship}）</span>
                  </div>
                );
              }
              return `${personNo}_${staffName}`;
            },
          }}
        ></QRichTable>

        <QCard headerStyle={{ padding: '0' }} bodyStyle={{ padding: '15px 0 0 0' }}>
          <div slot="title">关系图谱</div>
          <div class="h-500px border border-#eee border-solid overflow-hidden">
            <RelationshipChart dataSource={relationPathGraphData} />
          </div>
        </QCard>
      </QCard>
    );
  },
});

const RiskContent = defineComponent({
  name: 'RiskContent',
  props: {
    companyInfo: {
      type: Object as PropType<{ keyNo: string; name: string }>,
      default: () => ({}),
    },
    details: {
      type: Object,
      default: () => ({}),
    },
    snapshotId: {
      type: String,
      default: '',
    },
    id: {
      type: Number,
      required: true,
    },
    // 展示的内容，默认展示所有维度,
    showDimension: {
      type: String,
      default: undefined,
    },
    // 隐藏操作
    hideAction: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const track = useTrack();

    // 使用 computed 重构 keyList 数据处理逻辑
    const keyList = computed(() => {
      let keys = Object.entries(dimensionColumns)
        .sort((a, b) => (a[1] as any).sort - (b[1] as any).sort) // 按 sort 的值排序
        .map((entry) => entry[0]);

      if (props.showDimension) {
        keys = keys.filter((key) => key === props.showDimension);
      }

      return keys;
    });

    const checkHandler = async (key, item) => {
      track(createTrackEvent(8893, '潜在利益关系排查详情', '核实'));
      if (item.checking) {
        return;
      }
      item.checking = true;
      const { id: personId } = item.person || {};
      const { keyNo: personKeyNo } = item.companyPerson || {};
      try {
        const { status, verifyType, keyNo } = await personService.getPersonVerifyData({
          personId,
          keyNo: personKeyNo,
        });
        const isSync = [0, 1].includes(status);
        const openModal = isSync ? openCheckSyncModal : openStaffCheckModal;
        const investigationInfo = {
          key,
          diligenceId: props.id,
          snapshotId: props.snapshotId,
          companyId: props.companyInfo?.keyNo,
          companyName: props.companyInfo?.name,
        };
        const params = isSync
          ? {
              status,
              keyNo,
              verifyType,
              personId,
              ...investigationInfo,
            }
          : {
              keyNo: personKeyNo,
              personId,
              ...investigationInfo,
            };
        const { value = {}, type } = (await openModal(params)) as any;
        if (type === 'submit') {
          await potentialService.verifyPerson({
            ...pick(params, [
              'companyId',
              'companyName',
              'personId',
              'snapshotId',
              'diligenceId',
              'key',
              'keyNo',
              'status',
              'verifyType',
              'comment',
            ]),
            ...value,
          });
          message.success('核实成功！');
          track(createTrackEvent(8893, '潜在利益关系排查详情', '核实确定'));
          await nextTick();
          emit('rescan');
        }
      } catch (error) {
        console.log(error);
      }
      item.checking = false;
    };

    return {
      keyList,
      checkHandler,
    };
  },
  render() {
    return (
      <div>
        {this.keyList.map((key) => {
          const dataSource = this.details[key];
          if (!dataSource?.length) return null;

          return (
            <RiskDimensionCard
              key={key}
              dimensionKey={key}
              dataSource={dataSource}
              hideAction={this.hideAction}
              companyInfo={this.companyInfo}
              snapshotId={this.snapshotId}
              id={this.id}
              onCheck={this.checkHandler}
            />
          );
        })}
      </div>
    );
  },
});

export default RiskContent;
